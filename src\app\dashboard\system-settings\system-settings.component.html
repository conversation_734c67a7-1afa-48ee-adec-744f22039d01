<!-- System Settings -->
<div class="max-w-7xl mx-auto system-settings-container">
  <!-- Header Section -->
  <div class="mb-6 lg:mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl lg:text-3xl font-bold" [class]="getTextClasses()">System Settings</h1>
        <p class="mt-1 text-sm" [class]="getSecondaryTextClasses()">Configure system preferences and security settings</p>
      </div>
      <div class="mt-4 sm:mt-0 flex space-x-3">
        <button class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 text-sm font-medium">
          Save Changes
        </button>
        <button class="px-4 py-2 border rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium" [class]="getCardClasses()">
          Reset to Default
        </button>
      </div>
    </div>
  </div>

  <!-- Settings Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    
    <!-- General Settings -->
    <div class="rounded-lg border" [class]="getCardClasses()">
      <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <h3 class="text-lg font-semibold" [class]="getTextClasses()">General Settings</h3>
      </div>
      <div class="p-6 space-y-6">
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Library Name</label>
          <input type="text" value="Benedicto College Library" 
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Contact Email</label>
          <input type="email" value="<EMAIL>" 
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Phone Number</label>
          <input type="tel" value="+63 32 123 4567" 
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Address</label>
          <textarea rows="3" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">Benedicto College Campus, Cebu City, Philippines</textarea>
        </div>
      </div>
    </div>

    <!-- Security Settings -->
    <div class="rounded-lg border" [class]="getCardClasses()">
      <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <h3 class="text-lg font-semibold" [class]="getTextClasses()">Security Settings</h3>
      </div>
      <div class="p-6 space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">Two-Factor Authentication</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Require 2FA for admin accounts</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer" checked>
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
          </label>
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">Session Timeout</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Auto-logout after inactivity</p>
          </div>
          <select class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
            <option>30 minutes</option>
            <option>1 hour</option>
            <option>2 hours</option>
            <option>4 hours</option>
          </select>
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">Password Complexity</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Enforce strong passwords</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer" checked>
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
          </label>
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Maximum Login Attempts</label>
          <input type="number" value="5" min="3" max="10"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
      </div>
    </div>

    <!-- Library Policies -->
    <div class="rounded-lg border" [class]="getCardClasses()">
      <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <h3 class="text-lg font-semibold" [class]="getTextClasses()">Library Policies</h3>
      </div>
      <div class="p-6 space-y-6">
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Default Loan Period (Days)</label>
          <input type="number" value="14" min="1" max="90"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Maximum Renewals</label>
          <input type="number" value="2" min="0" max="5"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Late Fee per Day (₱)</label>
          <input type="number" value="5" min="0" step="0.50"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Maximum Books per Student</label>
          <input type="number" value="3" min="1" max="10"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">Allow Reservations</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Enable book reservation system</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer" checked>
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
          </label>
        </div>
      </div>
    </div>

    <!-- Notification Settings -->
    <div class="rounded-lg border" [class]="getCardClasses()">
      <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <h3 class="text-lg font-semibold" [class]="getTextClasses()">Notification Settings</h3>
      </div>
      <div class="p-6 space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">Email Notifications</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Send email reminders</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer" checked>
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
          </label>
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">SMS Notifications</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Send SMS reminders</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer">
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
          </label>
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2" [class]="getTextClasses()">Reminder Days Before Due</label>
          <input type="number" value="3" min="1" max="7"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium" [class]="getTextClasses()">Overdue Notifications</p>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Daily reminders for overdue items</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer" checked>
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- System Information -->
  <div class="mt-8 rounded-lg border" [class]="getCardClasses()">
    <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <h3 class="text-lg font-semibold" [class]="getTextClasses()">System Information</h3>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <p class="text-sm font-medium" [class]="getSecondaryTextClasses()">System Version</p>
          <p class="text-lg font-semibold" [class]="getTextClasses()">v2.1.0</p>
        </div>
        <div>
          <p class="text-sm font-medium" [class]="getSecondaryTextClasses()">Last Updated</p>
          <p class="text-lg font-semibold" [class]="getTextClasses()">July 25, 2024</p>
        </div>
        <div>
          <p class="text-sm font-medium" [class]="getSecondaryTextClasses()">Database Size</p>
          <p class="text-lg font-semibold" [class]="getTextClasses()">2.3 GB</p>
        </div>
      </div>
    </div>
  </div>
</div>
