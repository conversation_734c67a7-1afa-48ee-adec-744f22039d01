import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../services/theme.service';

interface Student {
  id: string;
  name: string;
}

interface Book {
  title: string;
  author: string;
}

interface Reservation {
  id: string;
  student: Student;
  book: Book;
  reservedDate: string;
  holdUntil: string;
  priority: 'High' | 'Normal' | 'Low';
  status: 'Pending' | 'Ready' | 'Expired' | 'Fulfilled';
}

interface ReservationStats {
  activeReservations: number;
  readyForPickup: number;
  expiredHolds: number;
  fulfilledToday: number;
}

@Component({
  selector: 'app-reservations',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './reservations.component.html',
  styleUrls: ['./reservations.component.css']
})
export class ReservationsComponent implements OnInit {

  stats: ReservationStats = {
    activeReservations: 45,
    readyForPickup: 12,
    expiredHolds: 3,
    fulfilledToday: 8
  };

  reservations: Reservation[] = [
    {
      id: 'R001',
      student: { id: 'S2024001', name: '<PERSON>' },
      book: { title: 'Data Structures and Algorithms', author: '<PERSON>' },
      reservedDate: '2024-07-20',
      holdUntil: '2024-07-30',
      priority: 'High',
      status: 'Ready'
    },
    {
      id: 'R002',
      student: { id: 'S2024002', name: 'Miguel Torres' },
      book: { title: 'Organic Chemistry', author: 'Paula Bruice' },
      reservedDate: '2024-07-18',
      holdUntil: '2024-07-28',
      priority: 'Normal',
      status: 'Pending'
    },
    {
      id: 'R003',
      student: { id: 'S2024003', name: 'Isabella Garcia' },
      book: { title: 'World Literature', author: 'Various Authors' },
      reservedDate: '2024-07-15',
      holdUntil: '2024-07-25',
      priority: 'Low',
      status: 'Expired'
    },
    {
      id: 'R004',
      student: { id: 'S2024004', name: 'Diego Fernandez' },
      book: { title: 'Calculus: Early Transcendentals', author: 'James Stewart' },
      reservedDate: '2024-07-22',
      holdUntil: '2024-08-01',
      priority: 'High',
      status: 'Ready'
    },
    {
      id: 'R005',
      student: { id: 'S2024005', name: 'Carmen Reyes' },
      book: { title: 'Introduction to Psychology', author: 'David Myers' },
      reservedDate: '2024-07-19',
      holdUntil: '2024-07-29',
      priority: 'Normal',
      status: 'Pending'
    }
  ];

  constructor(private themeService: ThemeService) { }

  // Getter for dark mode state from theme service
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode;
  }

  ngOnInit(): void {
    // Component initialization
  }

  getTextClasses(): string {
    return this.isDarkMode ? 'text-white' : 'text-gray-900';
  }

  getSecondaryTextClasses(): string {
    return this.isDarkMode ? 'text-gray-400' : 'text-gray-600';
  }

  getCardClasses(): string {
    return this.isDarkMode 
      ? 'bg-gray-800 border-gray-700 text-white' 
      : 'bg-white border-gray-200 text-gray-900';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Ready':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Expired':
        return 'bg-red-100 text-red-800';
      case 'Fulfilled':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Normal':
        return 'bg-blue-100 text-blue-800';
      case 'Low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
