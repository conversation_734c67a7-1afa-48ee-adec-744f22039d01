@echo off
echo Starting Library Management System in Production Mode...

echo.
echo Building frontend for production...
call npm run build:prod

echo.
echo Starting backend server...
start "Backend Server" cmd /k "cd backend-api && npm start"

echo.
echo Starting frontend server (serving built files)...
start "Frontend Server" cmd /k "npx http-server dist/Library-Management-System-AI/browser -p 4200 -c-1 --cors"

echo.
echo Production servers started!
echo Frontend: http://localhost:4200
echo Backend: http://localhost:3000
echo.
echo Press any key to stop all servers...
pause

echo Stopping servers...
taskkill /f /im node.exe
echo Servers stopped.
