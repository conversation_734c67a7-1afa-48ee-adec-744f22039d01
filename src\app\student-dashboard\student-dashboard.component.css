/* Modern Interactive Student Dashboard Styles */

/* Dark Mode Support */
[data-theme="dark"] .nav-link {
  color: #9ca3af;
}

[data-theme="dark"] .nav-link:hover {
  color: #f9fafb;
  background-color: #374151;
}

[data-theme="dark"] .nav-link.active {
  color: #60a5fa;
  background-color: #1e3a8a;
  border-right: 2px solid #60a5fa;
}

/* Navigation Styles */
.nav-link {
  color: #6b7280;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  color: #111827;
  background-color: #f3f4f6;
  transform: translateX(4px);
}

.nav-link.active {
  color: #2563eb;
  background-color: #eff6ff;
  border-right: 2px solid #2563eb;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

/* Logout Button Animation */
.logout-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Widget Animations */
.widget-card {
  transition: all 0.3s ease;
}

.widget-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-4px);
}

/* Weather Widget Animations */
#weather-icon {
  animation: pulse 2s infinite;
}

/* Card hover effects */
.card-hover {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse animation for notifications */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

/* Notification Badge Animation */
.notification-badge {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Sidebar fixed height and layout */
aside.w-64 {
  height: 100vh;
  position: sticky;
  top: 0;
}

/* Mobile responsive design */
@media (max-width: 1023px) {
  /* Make left sidebar mobile-friendly */
  aside.w-64 {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 40;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  /* Show left sidebar when mobile menu is open */
  aside.w-64[style*="translateX(0)"] {
    transform: translateX(0) !important;
  }

  /* Hide right sidebar on mobile */
  .right-sidebar {
    display: none;
  }

  /* Adjust main content for mobile */
  .main-content {
    margin-left: 0;
  }

  /* Mobile padding adjustments */
  main {
    padding: 1rem;
  }

  /* Mobile table scroll */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile grid adjustments */
  .grid {
    gap: 1rem;
  }
}

/* Desktop search button adjustments */
@media (min-width: 640px) {
  .search-container {
    flex-direction: row !important;
    align-items: flex-start;
    gap: 1rem;
  }

  .search-button {
    width: auto;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  /* Hide mobile menu button on desktop */
  .lg\:hidden {
    display: none;
  }

  /* Ensure sidebar is visible on desktop */
  aside.w-64 {
    position: sticky;
    top: 0;
    height: 100vh;
    transform: translateX(0) !important;
  }
}

/* Custom focus styles */
input:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Status indicators */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-available {
  background-color: #10b981;
}

.status-checked-out {
  background-color: #f59e0b;
}

.status-overdue {
  background-color: #ef4444;
}

/* Book card styles */
.book-card {
  border-left: 4px solid #3b82f6;
  transition: border-color 0.3s ease;
}

.book-card:hover {
  border-left-color: #1d4ed8;
}

.book-card.overdue {
  border-left-color: #ef4444;
}

.book-card.due-soon {
  border-left-color: #f59e0b;
}

/* Event type indicators */
.event-academic {
  border-left-color: #3b82f6;
}

.event-library {
  border-left-color: #10b981;
}

.event-workshop {
  border-left-color: #8b5cf6;
}

.event-social {
  border-left-color: #f59e0b;
}

/* Chat widget animations */
.chat-widget {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Typing indicator animation */
.typing-dot {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Weather widget styles */
.weather-widget {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* Quick action buttons */
.quick-action-btn {
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Schedule item styles */
.schedule-item {
  position: relative;
  padding-left: 1rem;
}

.schedule-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-full {
    width: 100%;
  }

  .mobile-stack {
    flex-direction: column;
  }
  
}

/* Dark mode specific adjustments */
[data-theme="dark"] .weather-widget {
  background: linear-gradient(135deg, #4c1d95 0%, #1e1b4b 100%);
}

[data-theme="dark"] .book-card {
  background-color: #374151;
}

[data-theme="dark"] .status-dot {
  box-shadow: 0 0 0 2px #374151;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-before: always;
  }
}
