<div [class]="isDarkMode ? 'text-white' : 'text-gray-900'">
  <!-- Toast Notifications -->
  <app-toast></app-toast>
  <!-- Header -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold mb-2">Student Management</h1>
        <p class="text-gray-600 dark:text-gray-400">Manage student accounts and library access</p>
      </div>
      <button (click)="openAddStudentModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add New Student
        </button>
      </div>
    </div>
  </div>

  <!-- Stats Overview - Small Boxes in Row -->
  <div class="flex gap-4 mb-6">
    <!-- Box 1: Total Students -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-green-100 dark:bg-green-900">
          <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Total Students</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ totalStudents }}</p>
      </div>
    </div>

    <!-- Box 2: Active Members -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-blue-100 dark:bg-blue-900">
          <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Active Members</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ activeStudents }}</p>
      </div>
    </div>

    <!-- Box 3: With Borrowed Books -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-orange-100 dark:bg-orange-900">
          <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Inactive Students</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ inactiveStudents }}</p>
      </div>
    </div>

    <!-- Box 4: Overdue Books -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-red-100 dark:bg-red-900">
          <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Blocked Students</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ blockedStudents }}</p>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input type="text" [(ngModel)]="searchTerm" (input)="onSearch()" placeholder="Search students by name, ID, or email..." class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
        </div>
      </div>
      <div class="flex gap-3">
        <select class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          <option>All Programs</option>
          <option>Computer Science</option>
          <option>Engineering</option>
          <option>Business</option>
          <option>Arts & Sciences</option>
        </select>
        <select class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          <option>All Years</option>
          <option>1st Year</option>
          <option>2nd Year</option>
          <option>3rd Year</option>
          <option>4th Year</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Students Table -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold">Student Lists</h3>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Student</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Student ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Program</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year Level</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <!-- Loading state -->
          <tr *ngIf="isLoading">
            <td colspan="6" class="px-6 py-8 text-center">
              <div class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-gray-500 dark:text-gray-400">Loading students...</span>
              </div>
            </td>
          </tr>

          <!-- No students found -->
          <tr *ngIf="!isLoading && filteredStudents.length === 0">
            <td colspan="6" class="px-6 py-8 text-center">
              <div class="text-gray-500 dark:text-gray-400">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No students found</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {{ searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding a new student.' }}
                </p>
              </div>
            </td>
          </tr>

          <!-- Student rows -->
          <tr *ngFor="let student of filteredStudents"
              class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              [class]="student.AccountStatus === 'Blocked' ? 'bg-red-50 dark:bg-red-900/20 opacity-75' : ''">
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-4">
                  <span class="text-green-600 dark:text-green-400 font-medium">
                    {{ getInitials(student.fullName || (student.FirstName + ' ' + student.LastName)) }}
                  </span>
                </div>
                <div>
                  <div class="text-sm font-medium"
                       [class]="student.AccountStatus === 'Blocked' ? 'text-red-600 dark:text-red-400 line-through' : ''">
                    {{ student.fullName || (student.FirstName + ' ' + student.LastName) }}
                  </div>
                  <div class="text-sm"
                       [class]="student.AccountStatus === 'Blocked' ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'">
                    {{ student.Email }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm"
                [class]="student.AccountStatus === 'Blocked' ? 'text-red-600 dark:text-red-400' : ''">
              {{ student.StudentID }}
            </td>
            <td class="px-6 py-4 text-sm"
                [class]="student.AccountStatus === 'Blocked' ? 'text-red-600 dark:text-red-400' : ''">
              {{ student.Course }}
            </td>
            <td class="px-6 py-4 text-sm"
                [class]="student.AccountStatus === 'Blocked' ? 'text-red-600 dark:text-red-400' : ''">
              {{ getYearLevelText(student.YearLevel) }}
            </td>
            <td class="px-6 py-4">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                    [ngClass]="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': student.EnrollmentStatus === 'Active' && student.AccountStatus === 'Allowed',
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': student.AccountStatus === 'Blocked',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': student.EnrollmentStatus === 'Inactive'
                    }">
                {{ getStatusText(student) }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm">
              <div class="flex space-x-2">
                <button (click)="openEditStudentModal(student)"
                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                  Edit
                </button>
                <button (click)="openViewStudentModal(student)"
                        class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 font-medium">
                  View
                </button>
                <button (click)="openBlockStudentModal(student)"
                        class="font-medium"
                        [class]="student.AccountStatus === 'Blocked' ? 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300' : 'text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300'">
                  {{ student.AccountStatus === 'Blocked' ? 'Unblock' : 'Block' }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Add Student Modal -->
  <div *ngIf="showAddStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto modal-content">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Add New Student</h2>
        <button (click)="closeAddStudentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="addStudent()" #studentForm="ngForm">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <!-- Student ID -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Student ID</label>
            <input 
              type="text" 
              [(ngModel)]="newStudent.studentID" 
              name="studentID"
              placeholder="e.g., 2022-99999 (leave empty for auto-generation)"
              (blur)="validateStudentID()"
              (input)="validateStudentID()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.studentID ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
            <p *ngIf="validationErrors.studentID" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.studentID }}
            </p>
            <p *ngIf="!validationErrors.studentID && !newStudent.studentID" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Leave empty to auto-generate (format: YYYY-NNNNN)
            </p>
          </div>

          <!-- First Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</label>
            <input 
              type="text" 
              [(ngModel)]="newStudent.firstName" 
              name="firstName"
              placeholder="Enter first name"
              required
              (blur)="validateFirstName()"
              (input)="validateFirstName()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.firstName ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
            <p *ngIf="validationErrors.firstName" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.firstName }}
            </p>
          </div>

          <!-- Last Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name *</label>
            <input 
              type="text" 
              [(ngModel)]="newStudent.lastName" 
              name="lastName"
              placeholder="Enter last name"
              required
              (blur)="validateLastName()"
              (input)="validateLastName()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.lastName ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
            <p *ngIf="validationErrors.lastName" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.lastName }}
            </p>
          </div>

          <!-- Middle Initial -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Middle Initial</label>
            <input 
              type="text" 
              [(ngModel)]="newStudent.middleInitial" 
              name="middleInitial"
              placeholder="M.I."
              maxlength="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Course -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Course *</label>
            <select 
              [(ngModel)]="newStudent.course" 
              name="course"
              required
              (change)="validateCourse()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.course ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
              <option value="">Select Course</option>
              <option value="BSIT">BSIT - Bachelor of Science in Information Technology</option>
              <option value="BSCS">BSCS - Bachelor of Science in Computer Science</option>
              <option value="BSE">BSE - Bachelor of Science in Engineering</option>
              <option value="BSBA">BSBA - Bachelor of Science in Business Administration</option>
              <option value="BSA">BSA - Bachelor of Science in Accountancy</option>
            </select>
            <p *ngIf="validationErrors.course" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.course }}
            </p>
          </div>

          <!-- Year Level -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Year Level *</label>
            <select 
              [(ngModel)]="newStudent.yearLevel" 
              name="yearLevel"
              required
              (change)="validateYearLevel()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.yearLevel ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
              <option value="">Select Year Level</option>
              <option value="1">1st Year</option>
              <option value="2">2nd Year</option>
              <option value="3">3rd Year</option>
              <option value="4">4th Year</option>
            </select>
            <p *ngIf="validationErrors.yearLevel" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.yearLevel }}
            </p>
          </div>

          <!-- Email -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
            <input 
              type="email" 
              [(ngModel)]="newStudent.email" 
              name="email"
              placeholder="<EMAIL>"
              required
              (blur)="validateEmail()"
              (input)="validateEmail()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.email ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
            <p *ngIf="validationErrors.email" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.email }}
            </p>
          </div>

          <!-- Phone Number -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number *</label>
            <input 
              type="tel" 
              [(ngModel)]="newStudent.phoneNumber" 
              name="phoneNumber"
              placeholder="09123456789"
              required
              (blur)="validatePhoneNumber()"
              (input)="validatePhoneNumber()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.phoneNumber ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
            <p *ngIf="validationErrors.phoneNumber" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.phoneNumber }}
            </p>
            <p *ngIf="!validationErrors.phoneNumber" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Format: 09XXXXXXXXX (11 digits starting with 09)
            </p>
          </div>

          <!-- Password -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password *</label>
            <input 
              type="password" 
              [(ngModel)]="newStudent.password" 
              name="password"
              placeholder="Enter password"
              required
              (blur)="validatePassword()"
              (input)="validatePassword()"
              [class]="'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white ' + 
                      (validationErrors.password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600')">
            
            <!-- Password Strength Indicator -->
            <div *ngIf="newStudent.password" class="mt-2">
              <div class="flex justify-between text-xs mb-1">
                <span class="text-gray-600 dark:text-gray-400">Password Strength:</span>
                <span [class]="getPasswordStrength().strength === 'Strong' ? 'text-green-600' : 
                              getPasswordStrength().strength === 'Good' ? 'text-blue-600' :
                              getPasswordStrength().strength === 'Fair' ? 'text-yellow-600' : 'text-red-600'">
                  {{ getPasswordStrength().strength }}
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div [class]="'h-2 rounded-full transition-all duration-300 ' + getPasswordStrength().color" 
                     [style.width]="getPasswordStrength().width"></div>
              </div>
            </div>

            <!-- Error or Help Text -->
            <p *ngIf="validationErrors.password" class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ validationErrors.password }}
            </p>
            <p *ngIf="!validationErrors.password" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Must be at least 6 characters with uppercase, lowercase, and number
            </p>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="flex justify-end space-x-3">
          <button 
            type="button" 
            (click)="closeAddStudentModal()"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors duration-200">
            Cancel
          </button>
          <button 
            type="submit" 
            (click)="addStudent()"
            [disabled]="!isFormValid() || isSubmitting"
            class="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors duration-200 flex items-center">
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? 'Adding...' : 'Add Student' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Edit Student Modal -->
  <div *ngIf="showEditStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto modal-content">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Student</h2>
        <button (click)="closeEditStudentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="updateStudent()" #editForm="ngForm">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <!-- Student ID (Read-only) -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Student ID</label>
            <input
              type="text"
              [(ngModel)]="editStudent.studentID"
              name="studentID"
              readonly
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed">
          </div>

          <!-- First Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</label>
            <input
              type="text"
              [(ngModel)]="editStudent.firstName"
              name="firstName"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Last Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name *</label>
            <input
              type="text"
              [(ngModel)]="editStudent.lastName"
              name="lastName"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Middle Initial -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Middle Initial</label>
            <input
              type="text"
              [(ngModel)]="editStudent.middleInitial"
              name="middleInitial"
              maxlength="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Course -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Course *</label>
            <select
              [(ngModel)]="editStudent.course"
              name="course"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="BSIT">BSIT - Bachelor of Science in Information Technology</option>
              <option value="BSCS">BSCS - Bachelor of Science in Computer Science</option>
              <option value="BSE">BSE - Bachelor of Science in Engineering</option>
              <option value="BSBA">BSBA - Bachelor of Science in Business Administration</option>
              <option value="BSA">BSA - Bachelor of Science in Accountancy</option>
            </select>
          </div>

          <!-- Year Level -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Year Level *</label>
            <select
              [(ngModel)]="editStudent.yearLevel"
              name="yearLevel"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="1">1st Year</option>
              <option value="2">2nd Year</option>
              <option value="3">3rd Year</option>
              <option value="4">4th Year</option>
            </select>
          </div>

          <!-- Email -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
            <input
              type="email"
              [(ngModel)]="editStudent.email"
              name="email"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Phone Number -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number</label>
            <input
              type="tel"
              [(ngModel)]="editStudent.phoneNumber"
              name="phoneNumber"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Enrollment Status -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Enrollment Status</label>
            <select
              [(ngModel)]="editStudent.enrollmentStatus"
              name="enrollmentStatus"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
            </select>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            (click)="closeEditStudentModal()"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors duration-200">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="isSubmitting"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors duration-200 flex items-center">
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? 'Updating...' : 'Update Student' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- View Student Modal -->
  <div *ngIf="showViewStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto modal-content">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Student Details</h2>
        <button (click)="closeViewStudentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div *ngIf="selectedStudent" class="space-y-6">
        <!-- Student Profile Header -->
        <div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <span class="text-blue-600 dark:text-blue-400 font-bold text-xl">
              {{ getInitials(selectedStudent.fullName || (selectedStudent.FirstName + ' ' + selectedStudent.LastName)) }}
            </span>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">
              {{ selectedStudent.fullName || (selectedStudent.FirstName + ' ' + selectedStudent.LastName) }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400">{{ selectedStudent.StudentID }}</p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"
                  [ngClass]="{
                    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': selectedStudent.EnrollmentStatus === 'Active' && selectedStudent.AccountStatus === 'Allowed',
                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': selectedStudent.AccountStatus === 'Blocked',
                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': selectedStudent.EnrollmentStatus === 'Inactive'
                  }">
              {{ getStatusText(selectedStudent) }}
            </span>
          </div>
        </div>

        <!-- Student Information Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Personal Information -->
          <div class="space-y-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-600 pb-2">
              Personal Information
            </h4>

            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">First Name</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.FirstName }}</p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Name</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.LastName }}</p>
              </div>

              <div *ngIf="selectedStudent.MiddleInitial">
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Middle Initial</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.MiddleInitial }}</p>
              </div>

              <div *ngIf="selectedStudent.Suffix && selectedStudent.Suffix !== 'N/A'">
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Suffix</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.Suffix }}</p>
              </div>
            </div>
          </div>

          <!-- Academic Information -->
          <div class="space-y-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-600 pb-2">
              Academic Information
            </h4>

            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Course</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.Course }}</p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Year Level</label>
                <p class="text-gray-900 dark:text-white">{{ getYearLevelText(selectedStudent.YearLevel) }}</p>
              </div>

              <div *ngIf="selectedStudent.Section">
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Section</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.Section }}</p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Enrollment Status</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.EnrollmentStatus }}</p>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-600 pb-2">
              Contact Information
            </h4>

            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.Email }}</p>
              </div>

              <div *ngIf="selectedStudent.PhoneNumber">
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone Number</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.PhoneNumber }}</p>
              </div>
            </div>
          </div>

          <!-- Account Information -->
          <div class="space-y-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-600 pb-2">
              Account Information
            </h4>

            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Status</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.AccountStatus }}</p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Created Date</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.CreatedAt | date:'medium' }}</p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</label>
                <p class="text-gray-900 dark:text-white">{{ selectedStudent.UpdatedAt | date:'medium' }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
          <button
            (click)="closeViewStudentModal()"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors duration-200">
            Close
          </button>
          <button
            (click)="openDeleteFromView()"
            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Delete Student
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Block/Unblock Student Modal -->
  <div *ngIf="showBlockStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 modal-content">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {{ selectedStudent?.AccountStatus === 'Blocked' ? 'Unblock' : 'Block' }} Student
        </h2>
        <button (click)="closeBlockStudentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div *ngIf="selectedStudent" class="mb-6">
        <!-- Student Info -->
        <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4">
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <span class="text-blue-600 dark:text-blue-400 font-medium">
              {{ getInitials(selectedStudent.fullName || (selectedStudent.FirstName + ' ' + selectedStudent.LastName)) }}
            </span>
          </div>
          <div>
            <p class="font-medium text-gray-900 dark:text-white">
              {{ selectedStudent.fullName || (selectedStudent.FirstName + ' ' + selectedStudent.LastName) }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ selectedStudent.StudentID }}</p>
          </div>
        </div>

        <!-- Warning Message -->
        <div class="p-4 rounded-lg mb-4"
             [class]="selectedStudent.AccountStatus === 'Blocked' ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'">
          <div class="flex items-start">
            <svg class="w-5 h-5 mt-0.5 mr-3"
                 [class]="selectedStudent.AccountStatus === 'Blocked' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    [attr.d]="selectedStudent.AccountStatus === 'Blocked' ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'"></path>
            </svg>
            <div>
              <h3 class="font-medium"
                  [class]="selectedStudent.AccountStatus === 'Blocked' ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
                {{ selectedStudent.AccountStatus === 'Blocked' ? 'Unblock Student Account' : 'Block Student Account' }}
              </h3>
              <p class="text-sm mt-1"
                 [class]="selectedStudent.AccountStatus === 'Blocked' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
                <span *ngIf="selectedStudent.AccountStatus === 'Blocked'">
                  This will restore the student's access to the library system. They will be able to borrow books and access library services.
                </span>
                <span *ngIf="selectedStudent.AccountStatus !== 'Blocked'">
                  This will prevent the student from accessing the library system. They won't be able to borrow books or access library services.
                </span>
              </p>
            </div>
          </div>
        </div>

        <!-- Current Status -->
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <strong>Current Status:</strong>
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-1"
                [ngClass]="{
                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': selectedStudent.EnrollmentStatus === 'Active' && selectedStudent.AccountStatus === 'Allowed',
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': selectedStudent.AccountStatus === 'Blocked',
                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': selectedStudent.EnrollmentStatus === 'Inactive'
                }">
            {{ getStatusText(selectedStudent) }}
          </span>
        </div>
      </div>

      <!-- Modal Actions -->
      <div class="flex justify-end space-x-3">
        <button
          (click)="closeBlockStudentModal()"
          class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors duration-200">
          Cancel
        </button>
        <button
          (click)="toggleStudentBlock()"
          [disabled]="isSubmitting"
          class="px-4 py-2 text-white rounded-lg transition-colors duration-200 flex items-center"
          [class]="selectedStudent?.AccountStatus === 'Blocked' ? 'bg-green-600 hover:bg-green-700 disabled:bg-gray-400' : 'bg-red-600 hover:bg-red-700 disabled:bg-gray-400'">
          <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isSubmitting ? 'Processing...' : (selectedStudent?.AccountStatus === 'Blocked' ? 'Unblock Student' : 'Block Student') }}
        </button>
      </div>
    </div>
  </div>

  <!-- Delete Student Modal -->
  <div *ngIf="showDeleteStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 modal-content">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold text-red-600 dark:text-red-400">Delete Student</h2>
        <button (click)="closeDeleteStudentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div *ngIf="selectedStudent" class="mb-6">
        <!-- Student Info -->
        <div class="flex items-center space-x-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
          <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <span class="text-red-600 dark:text-red-400 font-medium">
              {{ getInitials(selectedStudent.fullName || (selectedStudent.FirstName + ' ' + selectedStudent.LastName)) }}
            </span>
          </div>
          <div>
            <p class="font-medium text-red-900 dark:text-red-100">
              {{ selectedStudent.fullName || (selectedStudent.FirstName + ' ' + selectedStudent.LastName) }}
            </p>
            <p class="text-sm text-red-700 dark:text-red-300">{{ selectedStudent.StudentID }}</p>
          </div>
        </div>

        <!-- Warning Message -->
        <div class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
              <h3 class="font-medium text-red-800 dark:text-red-200">Permanent Deletion Warning</h3>
              <p class="text-sm text-red-700 dark:text-red-300 mt-1">
                This action cannot be undone. The student record will be permanently removed from the system, including:
              </p>
              <ul class="text-sm text-red-700 dark:text-red-300 mt-2 ml-4 list-disc">
                <li>Personal and academic information</li>
                <li>Library transaction history</li>
                <li>Account access and permissions</li>
                <li>All associated data</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Confirmation Input -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Type "DELETE" to confirm:
          </label>
          <input
            type="text"
            [(ngModel)]="deleteConfirmation"
            name="deleteConfirmation"
            placeholder="Type DELETE to confirm"
            class="w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white">

        </div>

        <!-- Current Status -->
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <strong>Current Status:</strong>
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-1"
                [ngClass]="{
                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': selectedStudent.EnrollmentStatus === 'Active' && selectedStudent.AccountStatus === 'Allowed',
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': selectedStudent.AccountStatus === 'Blocked',
                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': selectedStudent.EnrollmentStatus === 'Inactive'
                }">
            {{ getStatusText(selectedStudent) }}
          </span>
        </div>
      </div>

      <!-- Modal Actions -->
      <div class="flex justify-end space-x-3">
        <button
          (click)="closeDeleteStudentModal()"
          class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg transition-colors duration-200">
          Cancel
        </button>
        <button
          (click)="deleteStudent()"
          [disabled]="isSubmitting || deleteConfirmation !== 'DELETE'"
          class="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors duration-200 flex items-center">
          <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg *ngIf="!isSubmitting" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          {{ isSubmitting ? 'Deleting...' : 'Delete Permanently' }}
        </button>
      </div>
    </div>
  </div>

