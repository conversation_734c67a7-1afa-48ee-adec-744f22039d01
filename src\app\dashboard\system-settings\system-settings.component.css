/* System Settings Component Styles */
.system-settings-container {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Settings cards animation */
.grid > div {
  animation: slideInUp 0.6s ease forwards;
}

.grid > div:nth-child(1) { animation-delay: 0.1s; }
.grid > div:nth-child(2) { animation-delay: 0.2s; }
.grid > div:nth-child(3) { animation-delay: 0.3s; }
.grid > div:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form input styles */
input, textarea, select {
  transition: all 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Toggle switch styles */
.peer:checked + div {
  background-color: #DC2626;
}

.peer:focus + div {
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
}

/* Button hover effects */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Card hover effects */
.rounded-lg.border {
  transition: all 0.3s ease;
}

.rounded-lg.border:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark mode specific styles */
[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .rounded-lg.border:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Responsive design */
@media (max-width: 1024px) {
  .grid.grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .flex.space-x-3 {
    flex-direction: column;
    space-x: 0;
  }
  
  .flex.space-x-3 > * {
    margin-bottom: 0.75rem;
  }
  
  .grid.grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

/* Section dividers */
.space-y-6 > * + * {
  border-top: 1px solid;
  padding-top: 1.5rem;
}

[data-theme="dark"] .space-y-6 > * + * {
  border-color: #374151;
}

.space-y-6 > * + * {
  border-color: #E5E7EB;
}

/* Remove border from first item */
.space-y-6 > *:first-child {
  border-top: none !important;
  padding-top: 0 !important;
}
