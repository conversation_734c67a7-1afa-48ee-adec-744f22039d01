{"version": 2, "name": "benedicto-library-api", "builds": [{"src": "server.js", "use": "@vercel/node"}], "routes": [{"src": "/(.*)", "dest": "/server.js"}], "env": {"NODE_ENV": "production"}, "functions": {"server.js": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://benedictocollege-library.org"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, Accept, Origin, X-Requested-With"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}]}