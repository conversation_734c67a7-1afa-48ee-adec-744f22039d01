-- Add ProfilePhoto column to Students table
-- This migration adds a ProfilePhoto column to store the URL/path of student profile photos

USE library_management_system;

-- Add ProfilePhoto column to Students table
ALTER TABLE Students 
ADD COLUMN ProfilePhoto VARCHAR(500) NULL 
COMMENT 'URL or path to student profile photo' 
AFTER PhoneNumber;

-- Add index for better performance when querying by ProfilePhoto
CREATE INDEX idx_students_profile_photo ON Students(ProfilePhoto);

-- Update existing students with default profile photo (optional)
-- UPDATE Students SET ProfilePhoto = NULL WHERE ProfilePhoto IS NULL;

-- Show the updated table structure
DESCRIBE Students;
