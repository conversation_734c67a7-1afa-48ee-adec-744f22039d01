<!-- Borrowing & Returns Management -->
<div class="max-w-7xl mx-auto">
  <!-- Header Section -->
  <div class="mb-6 lg:mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl lg:text-3xl font-bold" [class]="getTextClasses()">Borrowing & Returns</h1>
        <p class="mt-1 text-sm" [class]="getSecondaryTextClasses()">Manage book loans, returns, and overdue items</p>
      </div>
      <div class="mt-4 sm:mt-0 flex space-x-3">
        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium">
          New Loan
        </button>
        <button class="px-4 py-2 border rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium" [class]="getCardClasses()">
          Quick Return
        </button>
      </div>
    </div>
  </div>

  <!-- Stats Overview - Small Boxes in Row -->
  <div class="flex gap-4 mb-8">
    <!-- Box 1: Active Loans -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-blue-100">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Active Loans</p>
        <p class="text-xl font-bold" [class]="getTextClasses()">{{ stats.activeLoans }}</p>
      </div>
    </div>

    <!-- Box 2: Returns Today -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-green-100">
          <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Returns Today</p>
        <p class="text-xl font-bold" [class]="getTextClasses()">{{ stats.returnsToday }}</p>
      </div>
    </div>

    <!-- Box 3: Overdue Items -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-red-100">
          <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Overdue Items</p>
        <p class="text-xl font-bold text-red-600">{{ stats.overdueItems }}</p>
      </div>
    </div>

    <!-- Box 4: Due Today -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-yellow-100">
          <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Due Today</p>
        <p class="text-xl font-bold text-yellow-600">{{ stats.dueToday }}</p>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="mb-6 p-4 rounded-lg border" [class]="getCardClasses()">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" [class]="getSecondaryTextClasses()" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input type="text" placeholder="Search by student name, book title, or ID..." 
                 class="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                 [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'">
        </div>
      </div>
      <div class="flex space-x-3">
        <select class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
          <option>All Status</option>
          <option>Active</option>
          <option>Overdue</option>
          <option>Due Today</option>
        </select>
        <select class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
          <option>Last 30 Days</option>
          <option>Last 7 Days</option>
          <option>Today</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Active Loans Table -->
  <div class="rounded-lg border overflow-hidden" [class]="getCardClasses()">
    <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <h3 class="text-lg font-semibold" [class]="getTextClasses()">Active Loans</h3>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Student</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Book</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Loan Date</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Due Date</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y" [class]="isDarkMode ? 'divide-gray-700' : 'divide-gray-200'">
          <tr *ngFor="let loan of loans" class="hover:bg-opacity-50" [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                  {{ loan.student.name.charAt(0) }}
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium" [class]="getTextClasses()">{{ loan.student.name }}</p>
                  <p class="text-sm" [class]="getSecondaryTextClasses()">{{ loan.student.id }}</p>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <div>
                <p class="text-sm font-medium" [class]="getTextClasses()">{{ loan.book.title }}</p>
                <p class="text-sm" [class]="getSecondaryTextClasses()">{{ loan.book.author }}</p>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm" [class]="getTextClasses()">{{ loan.loanDate }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm" [class]="getTextClasses()">{{ loan.dueDate }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs font-medium rounded-full" [ngClass]="getStatusClass(loan.status)">
                {{ loan.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
              <button class="text-blue-600 hover:text-blue-800 font-medium">Return</button>
              <button class="text-green-600 hover:text-green-800 font-medium">Renew</button>
              <button class="text-gray-600 hover:text-gray-800 font-medium">Details</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
