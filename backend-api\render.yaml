services:
  - type: web
    name: benedicto-library-api
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        fromService:
          type: web
          name: benedicto-library-api
          property: port
      - key: HOST
        value: 0.0.0.0
      - key: ALLOWED_ORIGINS
        value: https://benedictocollege-library.org
      - key: DB_HOST
        sync: false
      - key: DB_USER
        sync: false
      - key: DB_PASS
        sync: false
      - key: DB_NAME
        sync: false
      - key: OPENWEATHER_API_KEY
        sync: false
      - key: EMAIL_USER
        sync: false
      - key: EMAIL_PASS
        sync: false
    healthCheckPath: /
    autoDeploy: true
