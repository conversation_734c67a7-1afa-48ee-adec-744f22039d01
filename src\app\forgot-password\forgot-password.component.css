/* Forgot Password Page Styles - Benedicto College Library Management System */

/* Responsive design with 800px breakpoint */

/* Default: Mobile view (below 800px) - Form only, centered */
main {
  flex-direction: column;
}

main > div:first-child {
  display: none !important;
}

main > div:last-child {
  width: 100% !important;
}

/* Desktop view (800px and above) - Logo + Form side by side */
@media (min-width: 800px) {
  main {
    flex-direction: row !important;
  }

  main > div:first-child {
    display: flex !important;
    width: 40% !important;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  main > div:last-child {
    width: 60% !important;
  }
}

/* Enhanced form styling */
.form-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Step indicator styling */
.step-indicator {
  transition: all 0.3s ease;
}

.step-active {
  transform: scale(1.1);
}

/* Input field enhancements */
input:focus, select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button hover effects */
button:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Success animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-message {
  animation: fadeInUp 0.5s ease-out;
}

/* Error message styling */
.error-message {
  animation: fadeInUp 0.3s ease-out;
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .form-card {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .step-indicator {
    font-size: 0.875rem;
  }
  
  input, select, button {
    font-size: 0.875rem;
  }
}

/* Theme color utilities for dynamic styling */
.text-student-primary { color: #ea580c; }
.text-student-secondary { color: #c2410c; }
.bg-student-primary { background-color: #ea580c; }
.bg-student-secondary { background-color: #c2410c; }
.border-student-focus { border-color: #3b82f6; }
.ring-student { --tw-ring-color: rgb(147 197 253); }

.text-faculty-primary { color: #16a34a; }
.text-faculty-secondary { color: #15803d; }
.bg-faculty-primary { background-color: #16a34a; }
.bg-faculty-secondary { background-color: #15803d; }
.border-faculty-focus { border-color: #16a34a; }
.ring-faculty { --tw-ring-color: rgb(134 239 172); }

.text-admin-primary { color: #dc2626; }
.text-admin-secondary { color: #b91c1c; }
.bg-admin-primary { background-color: #dc2626; }
.bg-admin-secondary { background-color: #b91c1c; }
.border-admin-focus { border-color: #dc2626; }
.ring-admin { --tw-ring-color: rgb(254 202 202); }

/* Focus states for accessibility */
input:focus-visible, select:focus-visible, button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Disabled state styling */
input:disabled, select:disabled, button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Custom scrollbar for select dropdown */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Form validation styling */
.invalid {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.valid {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* Tooltip styling for better UX */
.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
