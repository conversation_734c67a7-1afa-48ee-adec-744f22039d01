# Database Configuration
DB_HOST=localhost
DB_USER=lms_user
DB_PASS=lms2026
DB_NAME=dblibrary

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS Configuration (comma-separated list)
# For development (same machine)
ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,http://127.0.0.1:4200,http://127.0.0.1:3000

# For production (replace with your actual domains)
# ALLOWED_ORIGINS=https://your-frontend-domain.com,https://your-backend-domain.com

# For separated deployment (different machines/containers)
# ALLOWED_ORIGINS=http://frontend-server:4200,http://backend-server:3000

# API Keys
OPENWEATHER_API_KEY=********************************

# Security Settings
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-key-here

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM_NAME=Benedicto College Library
EMAIL_FROM_ADDRESS=<EMAIL>

# OTP Configuration
OTP_EXPIRY_MINUTES=10
RESET_TOKEN_EXPIRY_HOURS=1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5
