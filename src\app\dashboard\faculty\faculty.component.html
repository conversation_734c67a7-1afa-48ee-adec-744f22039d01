<!-- Faculty Management -->
<div class="max-w-7xl mx-auto faculty-container">
  <!-- Header Section -->
  <div class="mb-6 lg:mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl lg:text-3xl font-bold" [class]="getTextClasses()">Faculty Management</h1>
        <p class="mt-1 text-sm" [class]="getSecondaryTextClasses()">Manage faculty accounts and permissions</p>
      </div>
      <div class="mt-4 sm:mt-0 flex space-x-3">
        <button
          class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium">
          Add Faculty
        </button>
        <button class="px-4 py-2 border rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium"
          [class]="getCardClasses()">
          Import CSV
        </button>
      </div>
    </div>
  </div>

  <!-- Stats Overview - Small Boxes in Row -->
  <div class="flex gap-4 mb-8">
    <!-- Box 1: Total Faculty -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-indigo-100">
          <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
            </path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Total Faculty</p>
        <p class="text-xl font-bold" [class]="getTextClasses()">{{ stats.totalFaculty }}</p>
      </div>
    </div>

    <!-- Box 2: Active Faculty -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-green-100">
          <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Active Faculty</p>
        <p class="text-xl font-bold" [class]="getTextClasses()">{{ stats.activeFaculty }}</p>
      </div>
    </div>

    <!-- Box 3: Departments -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-blue-100">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
            </path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Departments</p>
        <p class="text-xl font-bold" [class]="getTextClasses()">{{ stats.departments }}</p>
      </div>
    </div>

    <!-- Box 4: Pending Approvals -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300" [class]="getCardClasses()">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-yellow-100">
          <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1" [class]="getSecondaryTextClasses()">Pending Approvals</p>
        <p class="text-xl font-bold text-yellow-600">{{ stats.pendingApprovals }}</p>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="mb-6 p-4 rounded-lg border" [class]="getCardClasses()">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" [class]="getSecondaryTextClasses()"
            fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input type="text" placeholder="Search faculty by name, email, or department..."
            class="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'">
        </div>
      </div>
      <div class="flex space-x-3">
        <select class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
          <option>All Departments</option>
          <option>Computer Science</option>
          <option>Mathematics</option>
          <option>English</option>
          <option>Science</option>
        </select>
        <select class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'">
          <option>All Status</option>
          <option>Active</option>
          <option>Inactive</option>
          <option>Pending</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Faculty Table -->
  <div class="rounded-lg border overflow-hidden" [class]="getCardClasses()">
    <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <h3 class="text-lg font-semibold" [class]="getTextClasses()">Faculty Lists</h3>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Faculty</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Department</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Position</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Email</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Join Date</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
              [class]="getSecondaryTextClasses()">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y" [class]="isDarkMode ? 'divide-gray-700' : 'divide-gray-200'">
          <tr *ngFor="let faculty of facultyMembers" class="hover:bg-opacity-50"
            [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div
                  class="w-10 h-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-medium">
                  {{ getInitials(faculty.name) }}
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium" [class]="getTextClasses()">{{ faculty.name }}</p>
                  <p class="text-sm" [class]="getSecondaryTextClasses()">{{ faculty.employeeId }}</p>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm" [class]="getTextClasses()">{{ faculty.department }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm" [class]="getTextClasses()">{{ faculty.position }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm" [class]="getTextClasses()">{{ faculty.email }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm" [class]="getTextClasses()">{{ faculty.joinDate }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs font-medium rounded-full" [ngClass]="getStatusClass(faculty.status)">
                {{ faculty.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
              <button class="text-indigo-600 hover:text-indigo-800 font-medium">Edit</button>
              <button class="text-blue-600 hover:text-blue-800 font-medium">Permissions</button>
              <button class="text-gray-600 hover:text-gray-800 font-medium">Details</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>