import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../services/theme.service';

@Component({
  selector: 'app-system-settings',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './system-settings.component.html',
  styleUrls: ['./system-settings.component.css']
})
export class SystemSettingsComponent implements OnInit {

  constructor(private themeService: ThemeService) { }

  // Getter for dark mode state from theme service
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode;
  }

  ngOnInit(): void {
    // Component initialization
  }

  getTextClasses(): string {
    return this.themeService.getTextClasses();
  }

  getSecondaryTextClasses(): string {
    return this.themeService.getSecondaryTextClasses();
  }

  getCardClasses(): string {
    return this.themeService.getCardClasses();
  }
}
