/* Students Component Styles */
.student-avatar {
  transition: transform 0.2s ease;
}

.student-avatar:hover {
  transform: scale(1.1);
}

.student-row {
  transition: all 0.2s ease;
}

.student-row:hover {
  background-color: rgba(34, 197, 94, 0.05);
  transform: translateX(2px);
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-suspended {
  background-color: #fef3c7;
  color: #92400e;
}

.status-inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Dark mode status badges */
[data-theme="dark"] .status-active {
  background-color: #14532d;
  color: #bbf7d0;
}

[data-theme="dark"] .status-suspended {
  background-color: #451a03;
  color: #fde68a;
}

[data-theme="dark"] .status-inactive {
  background-color: #7f1d1d;
  color: #fecaca;
}

/* Search input focus */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Action buttons */
.action-btn {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Stats cards animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: slideInUp 0.6s ease forwards;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

/* Table hover effects */
.table-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

[data-theme="dark"] .table-header {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .student-row {
    font-size: 0.875rem;
  }
  
  .student-avatar {
    width: 2rem;
    height: 2rem;
  }
  
  .action-btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
}
/* Modal Styles */
.modal-overlay {
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

.modal-content {
  animation: slideInDown 0.3s ease;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Form input focus effects */
.form-input:focus {
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
  border-color: #10b981;
}

/* Submit button loading state */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Form validation styles */
.form-input.ng-invalid.ng-touched {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.ng-valid.ng-touched {
  border-color: #10b981;
}

/* Modal responsive design */
@media (max-width: 640px) {
  .modal-content {
    margin: 1rem;
    width: calc(100% - 2rem);
    max-height: calc(100vh - 2rem);
  }
  
  .modal-grid {
    grid-template-columns: 1fr;
  }
}