/* Admins Component Styles */
.admin-avatar {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.admin-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-row {
  transition: all 0.2s ease;
}

.admin-row:hover {
  background-color: rgba(147, 51, 234, 0.05);
  transform: translateX(2px);
}

/* Role badges */
.role-super-admin {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  color: #991b1b;
  border: 1px solid #f87171;
}

.role-admin {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  color: #7c3aed;
  border: 1px solid #a78bfa;
}

.role-librarian {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
  border: 1px solid #60a5fa;
}

/* Dark mode role badges */
[data-theme="dark"] .role-super-admin {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  color: #fecaca;
}

[data-theme="dark"] .role-admin {
  background: linear-gradient(135deg, #581c87 0%, #7c3aed 100%);
  color: #e9d5ff;
}

[data-theme="dark"] .role-librarian {
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  color: #dbeafe;
}

/* Status indicators */
.status-active {
  position: relative;
}

.status-active::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Search input focus */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

/* Action buttons */
.action-btn {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Permission button special styling */
.permission-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.permission-btn:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

/* Stats cards animation */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: slideInDown 0.6s ease forwards;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

/* Table enhancements */
.table-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e2e8f0;
}

[data-theme="dark"] .table-header {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  border-bottom: 2px solid #475569;
}

/* Security badge */
.security-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.security-badge svg {
  width: 0.75rem;
  height: 0.75rem;
  margin-right: 0.25rem;
}
