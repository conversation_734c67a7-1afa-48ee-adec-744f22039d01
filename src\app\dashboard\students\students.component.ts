import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ApiService } from '../../services/api.service';
import { ToastService } from '../../services/toast.service';
import { ToastComponent } from '../../components/toast/toast.component';
import { ThemeService } from '../../services/theme.service';
import { Subscription } from 'rxjs';

// Interface for Student data from database
interface Student {
  StudentID: string;
  FirstName: string;
  LastName: string;
  MiddleInitial?: string;
  Suffix?: string;
  Course: string;
  YearLevel: number;
  Section?: string;
  Email: string;
  PhoneNumber?: string;
  EnrollmentStatus: 'Active' | 'Inactive';
  AccountStatus: 'Allowed' | 'Blocked';
  CreatedAt: string;
  UpdatedAt: string;
  fullName?: string; // Computed field from backend
}

@Component({
  selector: 'app-students',
  standalone: true,
  imports: [CommonModule, FormsModule, ToastComponent],
  templateUrl: './students.component.html',
  styleUrls: ['./students.component.css']
})
export class StudentsComponent implements OnInit, OnDestroy {
  private themeSubscription: Subscription = new Subscription();
  showAddStudentModal: boolean = false;
  showEditStudentModal: boolean = false;
  showViewStudentModal: boolean = false;
  showBlockStudentModal: boolean = false;
  showDeleteStudentModal: boolean = false;
  isSubmitting: boolean = false;
  isLoading: boolean = false;

  newStudent = {
    studentID: '',
    firstName: '',
    lastName: '',
    middleInitial: '',
    suffix: '',
    course: '',
    yearLevel: '',
    section: '',
    email: '',
    phoneNumber: '',
    password: ''
  };

  // Validation errors object
  validationErrors = {
    studentID: '',
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    password: '',
    course: '',
    yearLevel: ''
  };

  // Real students data from database
  students: Student[] = [];
  filteredStudents: Student[] = [];
  searchTerm: string = '';

  // Selected student for modals
  selectedStudent: Student | null = null;
  editStudent: any = {};
  deleteConfirmation: string = '';

  // Statistics
  totalStudents: number = 0;
  activeStudents: number = 0;
  inactiveStudents: number = 0;
  blockedStudents: number = 0;

  constructor(
    private apiService: ApiService,
    private toastService: ToastService,
    private themeService: ThemeService
  ) { }



  // Getter for dark mode state from theme service
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode;
  }

  ngOnInit(): void {
    // Load students from database
    this.loadStudents();
  }

  ngOnDestroy(): void {
    this.themeSubscription.unsubscribe();
  }

  // Load students from database
  loadStudents(): void {
    this.isLoading = true;
    console.log('🔄 Loading students from database...');

    this.apiService.get('/auth/get-all-students').subscribe({
      next: (response: any) => {
        console.log('✅ Students loaded successfully:', response);
        if (response.success && response.data) {
          this.students = response.data;
          this.filteredStudents = [...this.students];
          this.updateStatistics();
          console.log(`📚 ${response.count} students loaded from database successfully`);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Failed to load students:', error);
        this.toastService.error('Failed to load students from database');
        this.isLoading = false;
      }
    });
  }

  // Update statistics based on loaded students
  updateStatistics(): void {
    this.totalStudents = this.students.length;
    this.activeStudents = this.students.filter(s => s.EnrollmentStatus === 'Active' && s.AccountStatus === 'Allowed').length;
    this.inactiveStudents = this.students.filter(s => s.EnrollmentStatus === 'Inactive').length;
    this.blockedStudents = this.students.filter(s => s.AccountStatus === 'Blocked').length;
  }

  // Search functionality
  onSearch(): void {
    if (!this.searchTerm.trim()) {
      this.filteredStudents = [...this.students];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredStudents = this.students.filter(student =>
      student.fullName?.toLowerCase().includes(term) ||
      student.StudentID.toLowerCase().includes(term) ||
      student.Email.toLowerCase().includes(term) ||
      student.Course.toLowerCase().includes(term)
    );
  }

  openAddStudentModal(): void {
    console.log('🔓 Opening add student modal');
    this.showAddStudentModal = true;
    this.resetForm();
  }

  closeAddStudentModal(): void {
    this.showAddStudentModal = false;
    this.resetForm();
  }

  resetForm(): void {
    this.newStudent = {
      studentID: '',
      firstName: '',
      lastName: '',
      middleInitial: '',
      suffix: '',
      course: '',
      yearLevel: '',
      section: '',
      email: '',
      phoneNumber: '',
      password: ''
    };
    this.validationErrors = {
      studentID: '',
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      password: '',
      course: '',
      yearLevel: ''
    };
    this.isSubmitting = false;
  }

  addStudent(): void {
    console.log('🔄 Add student method called');
    console.log('📝 Current form data:', this.newStudent);

    // Validate form before submission
    if (!this.isFormValid()) {
      console.log('❌ Form validation failed');
      console.log('🔍 Validation errors:', this.validationErrors);
      this.toastService.error('Please fix the errors in the form before submitting');
      return;
    }

    if (this.isSubmitting) {
      console.log('⏳ Already submitting, ignoring duplicate request');
      return;
    }

    this.isSubmitting = true;
    console.log('✅ Form validation passed, submitting...');

    // Convert yearLevel to number for API
    const studentData = {
      studentID: this.newStudent.studentID || '', // Allow empty for auto-generation
      firstName: this.newStudent.firstName,
      lastName: this.newStudent.lastName,
      middleInitial: this.newStudent.middleInitial || '',
      suffix: this.newStudent.suffix || '',
      course: this.newStudent.course,
      yearLevel: parseInt(this.newStudent.yearLevel),
      section: this.newStudent.section || '',
      email: this.newStudent.email,
      phoneNumber: this.newStudent.phoneNumber || '',
      password: this.newStudent.password
    };

    // Use the API service to register the student
    console.log('🚀 Submitting student data to API:', studentData);
    this.apiService.post('/auth/register-student', studentData).subscribe({
      next: (response: any) => {
        console.log('✅ API Response received:', response);

        if (response && response.success) {
          // Close modal and show success message
          this.closeAddStudentModal();
          const studentName = `${studentData.firstName} ${studentData.lastName}`;
          this.toastService.success(`Student ${studentName} added successfully!`);

          // Reload students from database to get the latest data
          this.loadStudents();
        } else {
          console.log('❌ API returned unsuccessful response:', response);
          this.isSubmitting = false;
          this.toastService.error('Failed to add student. Please try again.');
        }
      },
      error: (error) => {
        console.error('❌ API Error:', error);
        this.isSubmitting = false;
        this.toastService.error('Failed to add student. Please try again.');
      }
    });
  }

  private getYearSuffix(year: number): string {
    switch(year) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      case 4: return 'th';
      default: return 'th';
    }
  }

  // Modal Methods
  openEditStudentModal(student: Student): void {
    this.selectedStudent = student;
    this.editStudent = {
      studentID: student.StudentID,
      firstName: student.FirstName,
      lastName: student.LastName,
      middleInitial: student.MiddleInitial || '',
      suffix: student.Suffix || '',
      course: student.Course,
      yearLevel: student.YearLevel.toString(),
      section: student.Section || '',
      email: student.Email,
      phoneNumber: student.PhoneNumber || '',
      enrollmentStatus: student.EnrollmentStatus,
      accountStatus: student.AccountStatus
    };
    this.showEditStudentModal = true;
  }

  closeEditStudentModal(): void {
    this.showEditStudentModal = false;
    this.selectedStudent = null;
    this.editStudent = {};
  }

  openViewStudentModal(student: Student): void {
    this.selectedStudent = student;
    this.showViewStudentModal = true;
  }

  closeViewStudentModal(): void {
    this.showViewStudentModal = false;
    this.selectedStudent = null;
  }

  openBlockStudentModal(student: Student): void {
    this.selectedStudent = student;
    this.showBlockStudentModal = true;
  }

  closeBlockStudentModal(): void {
    this.showBlockStudentModal = false;
    this.selectedStudent = null;
  }

  openDeleteStudentModal(student: Student): void {
    this.selectedStudent = student;
    this.showDeleteStudentModal = true;
  }

  closeDeleteStudentModal(): void {
    this.showDeleteStudentModal = false;
    this.selectedStudent = null;
    this.deleteConfirmation = '';
  }

  // Method to handle transition from view modal to delete modal
  openDeleteFromView(): void {
    if (!this.selectedStudent) {
      return;
    }

    // Store the student reference before closing view modal
    const studentToDelete = this.selectedStudent;

    // Close view modal
    this.showViewStudentModal = false;

    // Open delete modal with stored reference
    this.selectedStudent = studentToDelete;
    this.showDeleteStudentModal = true;
  }



  // Update student information
  updateStudent(): void {
    if (!this.selectedStudent) return;

    this.isSubmitting = true;
    const updateData = {
      studentID: this.editStudent.studentID,
      firstName: this.editStudent.firstName,
      lastName: this.editStudent.lastName,
      middleInitial: this.editStudent.middleInitial,
      suffix: this.editStudent.suffix,
      course: this.editStudent.course,
      yearLevel: parseInt(this.editStudent.yearLevel),
      section: this.editStudent.section,
      email: this.editStudent.email,
      phoneNumber: this.editStudent.phoneNumber,
      enrollmentStatus: this.editStudent.enrollmentStatus,
      accountStatus: this.editStudent.accountStatus
    };

    // Call API to update student
    this.apiService.put(`/auth/update-student/${this.selectedStudent.StudentID}`, updateData).subscribe({
      next: (response: any) => {
        if (response.success) {
          const studentName = `${this.editStudent.firstName} ${this.editStudent.lastName}`;
          this.toastService.success(`Student ${studentName} updated successfully!`);
          this.closeEditStudentModal();
          this.loadStudents(); // Reload the list
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('❌ Failed to update student:', error);
        this.toastService.error('Failed to update student. Please try again.');
        this.isSubmitting = false;
      }
    });
  }

  // Toggle student block status
  toggleStudentBlock(): void {
    if (!this.selectedStudent) {
      console.log('❌ No student selected for blocking/unblocking');
      return;
    }

    this.isSubmitting = true;
    const newStatus = this.selectedStudent.AccountStatus === 'Blocked' ? 'Allowed' : 'Blocked';
    const action = newStatus === 'Blocked' ? 'block' : 'unblock';
    const studentName = this.selectedStudent.fullName || `${this.selectedStudent.FirstName} ${this.selectedStudent.LastName}`;
    const studentID = this.selectedStudent.StudentID;

    console.log(`🔄 ${action}ing student:`, studentID, studentName, 'New status:', newStatus);

    // Use the update-student endpoint to change account status
    // Only send fields that are actually changing to avoid validation issues
    const updateData: any = {
      accountStatus: newStatus
    };

    // Only include phone number if it's in the correct format or empty
    const phoneNumber = this.selectedStudent.PhoneNumber;
    if (phoneNumber && phoneNumber !== 'N/A') {
      // Check if phone number matches the required format
      if (/^09\d{9}$/.test(phoneNumber)) {
        updateData.phoneNumber = phoneNumber;
      }
      // If it doesn't match, don't include it to avoid validation error
    }

    console.log('📝 Update data being sent:', updateData);

    this.apiService.put(`/auth/update-student/${studentID}`, updateData).subscribe({
      next: (response: any) => {
        console.log(`✅ ${action} response:`, response);
        if (response && response.success) {
          const actionText = action === 'block' ? 'blocked' : 'unblocked';
          this.toastService.success(`Student ${studentName} ${actionText} successfully!`);
          this.closeBlockStudentModal();
          this.loadStudents(); // Reload the list
        } else {
          console.log(`❌ ${action} failed:`, response);
          const actionText = action === 'block' ? 'block' : 'unblock';
          this.toastService.error(`Failed to ${actionText} student. Please try again.`);
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error(`❌ ${action} API error:`, error);
        this.isSubmitting = false;
        const actionText = action === 'block' ? 'block' : 'unblock';
        this.toastService.error(`Failed to ${actionText} student. Please try again.`);
      }
    });
  }

  // Delete student permanently
  deleteStudent(): void {
    if (!this.selectedStudent) {
      return;
    }

    if (this.deleteConfirmation !== 'DELETE') {
      this.toastService.error('Please type "DELETE" to confirm');
      return;
    }

    this.isSubmitting = true;
    const studentName = this.selectedStudent.fullName || `${this.selectedStudent.FirstName} ${this.selectedStudent.LastName}`;
    const studentID = this.selectedStudent.StudentID;

    console.log('🗑️ Deleting student:', studentID, studentName);

    this.apiService.delete(`/auth/delete-student/${studentID}`).subscribe({
      next: (response: any) => {
        console.log('✅ Delete response:', response);
        if (response && response.success) {
          this.toastService.success(`Student ${studentName} deleted successfully!`);
          this.closeDeleteStudentModal();
          this.loadStudents(); // Reload the list
        } else {
          console.log('❌ Delete failed:', response);
          this.toastService.error('Failed to delete student. Please try again.');
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('❌ Delete API error:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          error: error.error,
          message: error.message
        });
        this.isSubmitting = false;

        this.toastService.error('Failed to delete student. Please try again.');
      }
    });
  }



  // Validation methods
  validateStudentID(): void {
    const studentID = this.newStudent.studentID.trim();
    // Student ID is optional - if provided, it must match the format
    if (studentID && !/^\d{4}-\d{5}$/.test(studentID)) {
      this.validationErrors.studentID = 'Student ID must be in format YYYY-NNNNN (e.g., 2025-00001) or leave empty for auto-generation';
    } else {
      this.validationErrors.studentID = '';
    }
  }

  validateFirstName(): void {
    const firstName = this.newStudent.firstName.trim();
    if (!firstName) {
      this.validationErrors.firstName = 'First name is required';
    } else if (firstName.length > 100) {
      this.validationErrors.firstName = 'First name must not exceed 100 characters';
    } else {
      this.validationErrors.firstName = '';
    }
  }

  validateLastName(): void {
    const lastName = this.newStudent.lastName.trim();
    if (!lastName) {
      this.validationErrors.lastName = 'Last name is required';
    } else if (lastName.length > 100) {
      this.validationErrors.lastName = 'Last name must not exceed 100 characters';
    } else {
      this.validationErrors.lastName = '';
    }
  }

  validateEmail(): void {
    const email = this.newStudent.email.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      this.validationErrors.email = 'Email is required';
    } else if (!emailRegex.test(email)) {
      this.validationErrors.email = 'Please provide a valid email address';
    } else {
      this.validationErrors.email = '';
    }
  }

  validatePhoneNumber(): void {
    const phoneNumber = this.newStudent.phoneNumber.trim();
    if (!phoneNumber) {
      this.validationErrors.phoneNumber = 'Phone number is required';
    } else if (!/^09\d{9}$/.test(phoneNumber)) {
      this.validationErrors.phoneNumber = 'Phone number must be in format 09XXXXXXXXX (11 digits starting with 09)';
    } else {
      this.validationErrors.phoneNumber = '';
    }
  }

  validatePassword(): void {
    const password = this.newStudent.password;
    if (!password) {
      this.validationErrors.password = 'Password is required';
    } else if (password.length < 6) {
      this.validationErrors.password = 'Password must be at least 6 characters long';
    } else if (!/(?=.*[a-z])/.test(password)) {
      this.validationErrors.password = 'Password must contain at least one lowercase letter';
    } else if (!/(?=.*[A-Z])/.test(password)) {
      this.validationErrors.password = 'Password must contain at least one uppercase letter';
    } else if (!/(?=.*\d)/.test(password)) {
      this.validationErrors.password = 'Password must contain at least one number';
    } else {
      this.validationErrors.password = '';
    }
  }

  validateCourse(): void {
    if (!this.newStudent.course) {
      this.validationErrors.course = 'Course is required';
    } else {
      this.validationErrors.course = '';
    }
  }

  validateYearLevel(): void {
    if (!this.newStudent.yearLevel) {
      this.validationErrors.yearLevel = 'Year level is required';
    } else {
      this.validationErrors.yearLevel = '';
    }
  }

  // Check if form is valid
  isFormValid(): boolean {
    // Validate all fields
    this.validateStudentID();
    this.validateFirstName();
    this.validateLastName();
    this.validateEmail();
    this.validatePhoneNumber();
    this.validatePassword();
    this.validateCourse();
    this.validateYearLevel();

    // Check if any errors exist
    return Object.values(this.validationErrors).every(error => error === '');
  }

  // Get password strength indicator
  getPasswordStrength(): { strength: string, color: string, width: string } {
    const password = this.newStudent.password;
    if (!password) return { strength: '', color: '', width: '0%' };

    let score = 0;
    if (password.length >= 6) score++;
    if (/(?=.*[a-z])/.test(password)) score++;
    if (/(?=.*[A-Z])/.test(password)) score++;
    if (/(?=.*\d)/.test(password)) score++;
    if (password.length >= 8) score++;

    switch (score) {
      case 0:
      case 1:
        return { strength: 'Very Weak', color: 'bg-red-500', width: '20%' };
      case 2:
        return { strength: 'Weak', color: 'bg-orange-500', width: '40%' };
      case 3:
        return { strength: 'Fair', color: 'bg-yellow-500', width: '60%' };
      case 4:
        return { strength: 'Good', color: 'bg-blue-500', width: '80%' };
      case 5:
        return { strength: 'Strong', color: 'bg-green-500', width: '100%' };
      default:
        return { strength: '', color: '', width: '0%' };
    }
  }

  // Helper method to get initials from full name
  getInitials(fullName: string): string {
    if (!fullName) return '??';
    const names = fullName.trim().split(' ');
    if (names.length === 1) return names[0].substring(0, 2).toUpperCase();
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  }

  // Helper method to get year level text
  getYearLevelText(yearLevel: number): string {
    if (!yearLevel) return 'N/A';
    const suffix = this.getYearSuffix(yearLevel);
    return `${yearLevel}${suffix} Year`;
  }

  // Helper method to get status text
  getStatusText(student: Student): string {
    if (student.AccountStatus === 'Blocked') return 'Blocked';
    if (student.EnrollmentStatus === 'Inactive') return 'Inactive';
    return 'Active';
  }

}
