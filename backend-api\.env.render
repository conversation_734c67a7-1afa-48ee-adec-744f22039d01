# Render Environment Configuration Template
# Copy these values to your Render service environment variables

# Database Configuration (Update with your database details)
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASS=your-database-password
DB_NAME=your-database-name

# Server Configuration (Ren<PERSON> will set PORT automatically)
NODE_ENV=production
HOST=0.0.0.0

# CORS Configuration - Allow your frontend domain
ALLOWED_ORIGINS=https://benedictocollege-library.org

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM_NAME=Benedicto College Library
EMAIL_FROM_ADDRESS=<EMAIL>

# OTP Configuration
OTP_EXPIRY_MINUTES=10
RESET_TOKEN_EXPIRY_HOURS=1

# OpenWeatherMap API
OPENWEATHER_API_KEY=********************************

# HuggingFace API Keys (Optional)
HF_1=
HF_2=
HF_3=
HF_4=
HF_5=
HF_6=

# Other API Keys (Optional)
OR_1=
OR_2=
OR_3=
OR_4=
OR_5=
OR_6=
