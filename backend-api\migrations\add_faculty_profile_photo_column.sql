-- Add ProfilePhoto column to Faculty table
-- This migration adds a ProfilePhoto column to store the URL/path of faculty profile photos

USE library_management_system;

-- Add ProfilePhoto column to Faculty table
ALTER TABLE faculty 
ADD COLUMN ProfilePhoto VARCHAR(500) NULL 
COMMENT 'URL or path to faculty profile photo' 
AFTER PhoneNumber;

-- Add index for better performance when querying by ProfilePhoto
CREATE INDEX idx_faculty_profile_photo ON faculty(ProfilePhoto);

-- Update existing faculty with default profile photo (optional)
-- UPDATE faculty SET ProfilePhoto = NULL WHERE ProfilePhoto IS NULL;

-- Show the updated table structure
DESCRIBE faculty;

-- Verify the change
SELECT COUNT(*) as total_faculty, 
       COUNT(ProfilePhoto) as faculty_with_photos 
FROM faculty;
