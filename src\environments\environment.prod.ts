// Environment configuration for production
export const environment = {
  production: true,
  apiUrl: 'https://api.benedictocollege-library.org/api/v1',
  backendUrl: 'https://api.benedictocollege-library.org',
  
  // API endpoints
  endpoints: {
    auth: '/auth',
    adminAuth: '/adminauth',
    facultyAuth: '/facultyauth',
    weather: '/weather',
    health: '/'
  },
  
  // Production settings
  enableLogging: false,
  enableDebugMode: false,
  
  // CORS settings for production
  allowedOrigins: [
    'https://benedictocollege-library.org',
    'https://api.benedictocollege-library.org'
  ],

  // Connection retry settings for production
  connectionRetry: {
    maxRetries: 5,
    retryDelay: 3000,
    timeout: 15000
  }
};
