// Environment configuration for production
export const environment = {
  production: true,
  apiUrl: 'https://benedicto-library-api.onrender.com/api/v1',
  backendUrl: 'https://benedicto-library-api.onrender.com',
  
  // API endpoints
  endpoints: {
    auth: '/auth',
    adminAuth: '/adminauth',
    facultyAuth: '/facultyauth',
    weather: '/weather',
    health: '/'
  },
  
  // Production settings
  enableLogging: false,
  enableDebugMode: false,
  
  // CORS settings for production
  allowedOrigins: [
    'https://benedictocollege-library.org',
    'https://benedicto-library-api.onrender.com'
  ],

  // Connection retry settings for production
  connectionRetry: {
    maxRetries: 5,
    retryDelay: 3000,
    timeout: 15000
  }
};
