<div class="min-h-screen p-6" [class]="isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold mb-2">Books & Catalog</h1>
        <p class="text-gray-600 dark:text-gray-400">Manage your library's book collection</p>
      </div>
      <div class="flex items-center gap-3">
        <button (click)="refreshBooks()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center" title="Refresh books list">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
        <button (click)="openAddBookModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add New Book
        </button>
      </div>
    </div>
  </div>



  <!-- Search and Filters -->
  <div class="rounded-lg p-6 shadow-sm border mb-8" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input type="text" placeholder="Search books by title, author, or ISBN..." class="w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" [class]="isDarkMode ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400' : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'">
        </div>
      </div>
      <div class="flex gap-3">
        <select class="px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" [class]="isDarkMode ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-300 bg-white text-gray-900'">
          <option>All Categories</option>
          <option>Fiction</option>
          <option>Non-Fiction</option>
          <option>Science</option>
          <option>History</option>
          <option>Technology</option>
        </select>
        <select class="px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" [class]="isDarkMode ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-300 bg-white text-gray-900'">
          <option>All Status</option>
          <option>Available</option>
          <option>Borrowed</option>
          <option>Reserved</option>
          <option>Maintenance</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Books Table -->
  <div class="rounded-lg shadow-sm border overflow-hidden" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
    <div class="px-6 py-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <h3 class="text-lg font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Book Collection</h3>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Book Details</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Category</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Location</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y" [class]="isDarkMode ? 'divide-gray-700' : 'divide-gray-200'">
          <!-- No books message -->
          <tr *ngIf="books.length === 0" class="hover:bg-opacity-50" [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'">
            <td colspan="5" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <h3 class="text-lg font-medium mb-2" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">No books found</h3>
                <p class="mb-4" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Get started by adding your first book to the catalog.</p>
                <button (click)="openAddBookModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                  Add Your First Book
                </button>
              </div>
            </td>
          </tr>

          <!-- Dynamic books from API -->
          <tr *ngFor="let book of books" class="hover:bg-opacity-50" [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'">
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="w-12 h-16 rounded flex items-center justify-center mr-4" [class]="isDarkMode ? 'bg-blue-900' : 'bg-blue-100'">
                  <svg class="w-6 h-6" [class]="isDarkMode ? 'text-blue-400' : 'text-blue-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                </div>
                <div>
                  <div class="text-sm font-medium" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">{{ book.Title }}</div>
                  <div class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'" *ngIf="book.Author">by {{ book.Author }}</div>
                  <div class="text-xs" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'" *ngIf="book.ISBN">ISBN: {{ book.ISBN }}</div>
                  <div class="text-xs" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                    <span *ngIf="book.CallNumber">Call Number: {{ book.CallNumber }}</span>
                    <span *ngIf="book.CallNumber && book.DeweyDecimal"> | </span>
                    <span *ngIf="book.DeweyDecimal">Dewey: {{ book.DeweyDecimal }}</span>
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-900'">
              <div *ngIf="book.Category">{{ book.Category }}</div>
              <div class="text-xs" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'" *ngIf="book.Subject">{{ book.Subject }}</div>
            </td>
            <td class="px-6 py-4">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                    [ngClass]="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': book.Status === 'Available',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': book.Status === 'Borrowed',
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': book.Status === 'Lost',
                      'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': book.Status === 'Damaged'
                    }">
                {{ book.Status }}
              </span>
              <div class="text-xs text-gray-500 mt-1">{{ book.Copies || 1 }} copies</div>
            </td>
            <td class="px-6 py-4 text-sm">{{ book.ShelfLocation || 'N/A' }}</td>
            <td class="px-6 py-4 text-sm">
              <div class="flex space-x-2">
                <button (click)="viewBook(book.BookID)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">View</button>
                <button (click)="editBook(book.BookID)" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300">Edit</button>
                <button (click)="deleteBook(book.BookID)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">Delete</button>
              </div>
            </td>
          </tr>


        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="totalBooks > 0" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <!-- Results info and Items per page selector -->
        <div class="flex items-center gap-4">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Showing {{ getDisplayRange() }} books
          </div>
          <div class="flex items-center gap-2">
            <label class="text-sm text-gray-700 dark:text-gray-300">Show:</label>
            <select
              [(ngModel)]="itemsPerPage"
              (ngModelChange)="onItemsPerPageChange()"
              class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="15">15</option>
              <option value="20">20</option>
              <option value="50">50</option>
            </select>
            <span class="text-sm text-gray-700 dark:text-gray-300">per page</span>
          </div>
        </div>

        <!-- Pagination controls -->
        <div class="flex items-center space-x-2">
          <!-- Previous button -->
          <button
            (click)="previousPage()"
            [disabled]="currentPage === 1"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <!-- Page numbers -->
          <button
            *ngFor="let page of getPageNumbers()"
            (click)="goToPage(page)"
            [class]="page === currentPage ?
              'px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-300 rounded-lg dark:bg-blue-900 dark:text-blue-200 dark:border-blue-600' :
              'px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white'">
            {{ page }}
          </button>

          <!-- Next button -->
          <button
            (click)="nextPage()"
            [disabled]="currentPage === totalPages"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Book Modal -->
  <div *ngIf="showAddBookModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-[800px] h-[500px] overflow-y-auto">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add New Book</h3>
          <button (click)="closeAddBookModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <form (ngSubmit)="onSubmitBook()" #bookForm="ngForm" class="p-4">
        <div class="space-y-3">
          <!-- Title (Full Width) -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              [(ngModel)]="newBook.Title"
              name="title"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
              [class.border-red-500]="!newBook.Title && isSubmitting"
              placeholder="Enter book title">
            <p *ngIf="!newBook.Title && isSubmitting" class="text-red-500 text-xs mt-1">Title is required</p>
          </div>

          <!-- Author, ISBN, and Category Row -->
          <div class="grid grid-cols-3 gap-3">
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Author</label>
              <input
                type="text"
                [(ngModel)]="newBook.Author"
                name="author"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Author name">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                ISBN <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                [(ngModel)]="newBook.ISBN"
                name="isbn"
                required
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                [class.border-red-500]="!newBook.ISBN && isSubmitting"
                placeholder="ISBN">
              <p *ngIf="!newBook.ISBN && isSubmitting" class="text-red-500 text-xs mt-1">Required</p>
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
              <select
                [(ngModel)]="newBook.Category"
                name="category"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
                <option value="">Select Category</option>
                <option value="Computer Science">Computer Science</option>
                <option value="Mathematics">Mathematics</option>
                <option value="History">History</option>
                <option value="Literature">Literature</option>
                <option value="Science">Science</option>
                <option value="Engineering">Engineering</option>
                <option value="Business">Business</option>
                <option value="Arts">Arts</option>
                <option value="Philosophy">Philosophy</option>
                <option value="Religion">Religion</option>
                <option value="Others">Others</option>
                <option value="Miscellaneous">Miscellaneous</option>
              </select>
            </div>
          </div>

          <!-- Subject and Publisher Row -->
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Subject</label>
              <input
                type="text"
                [(ngModel)]="newBook.Subject"
                name="subject"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Subject">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Publisher</label>
              <input
                type="text"
                [(ngModel)]="newBook.Publisher"
                name="publisher"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Publisher name">
            </div>
          </div>

          <!-- Years, Copies, Call Number, and Dewey Row -->
          <div class="grid grid-cols-4 gap-3">
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Published Year</label>
              <input
                type="number"
                [(ngModel)]="newBook.PublishedYear"
                name="publishedYear"
                min="1000"
                max="2030"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Year">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Copyright Year</label>
              <input
                type="number"
                [(ngModel)]="newBook.CopyrightYear"
                name="copyrightYear"
                min="1000"
                max="2030"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Year">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Copies</label>
              <input
                type="number"
                [(ngModel)]="newBook.Copies"
                name="copies"
                min="1"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="1">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
              <select
                [(ngModel)]="newBook.Status"
                name="status"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
                <option value="Available">Available</option>
                <option value="Borrowed">Borrowed</option>
                <option value="Lost">Lost</option>
                <option value="Damaged">Damaged</option>
              </select>
            </div>
          </div>

          <!-- Call Number, Dewey, Shelf Location, and Date Row -->
          <div class="grid grid-cols-4 gap-3">
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Call Number</label>
              <input
                type="text"
                [(ngModel)]="newBook.CallNumber"
                name="callNumber"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Call #">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Dewey Decimal</label>
              <input
                type="text"
                [(ngModel)]="newBook.DeweyDecimal"
                name="deweyDecimal"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Dewey #">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Shelf Location</label>
              <input
                type="text"
                [(ngModel)]="newBook.ShelfLocation"
                name="shelfLocation"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                placeholder="Shelf">
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Acquisition Date</label>
              <input
                type="date"
                [(ngModel)]="newBook.AcquisitionDate"
                name="acquisitionDate"
                class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
            </div>
          </div>

          <!-- Remarks -->
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Remarks</label>
            <textarea
              [(ngModel)]="newBook.Remarks"
              name="remarks"
              rows="2"
              class="w-full px-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm resize-none"
              placeholder="Any remarks or notes about the book"></textarea>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end gap-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            (click)="closeAddBookModal()"
            class="px-3 py-1.5 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors text-sm">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="!newBook.Title || !newBook.ISBN || isSubmitting"
            class="px-4 py-1.5 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-md transition-colors flex items-center text-sm">
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? 'Adding...' : 'Add Book' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- View Book Modal -->
  <div *ngIf="showViewBookModal && selectedBook" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Book Details</h3>
          <button (click)="closeViewBookModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Book Icon and Basic Info -->
          <div class="md:col-span-2 flex items-start space-x-4 mb-6">
            <div class="w-20 h-28 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <svg class="w-10 h-10 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ selectedBook.Title }}</h4>
              <p class="text-gray-600 dark:text-gray-400 mb-1" *ngIf="selectedBook.Author">by {{ selectedBook.Author }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-500" *ngIf="selectedBook.ISBN">ISBN: {{ selectedBook.ISBN }}</p>
              <div class="mt-3">
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                      [ngClass]="{
                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': selectedBook.Status === 'Available',
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': selectedBook.Status === 'Borrowed',
                        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': selectedBook.Status === 'Lost',
                        'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': selectedBook.Status === 'Damaged'
                      }">
                  {{ selectedBook.Status }}
                </span>
              </div>
            </div>
          </div>

          <!-- Book Details Grid -->
          <div class="space-y-4">
            <div *ngIf="selectedBook.Category">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
              <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.Category }}</p>
            </div>

            <div *ngIf="selectedBook.Subject">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Subject</label>
              <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.Subject }}</p>
            </div>

            <div *ngIf="selectedBook.Publisher">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Publisher</label>
              <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.Publisher }}</p>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div *ngIf="selectedBook.PublishedYear">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Published Year</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.PublishedYear }}</p>
              </div>
              <div *ngIf="selectedBook.CopyrightYear">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Copyright Year</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.CopyrightYear }}</p>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div *ngIf="selectedBook.CallNumber">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Call Number</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.CallNumber }}</p>
              </div>
              <div *ngIf="selectedBook.DeweyDecimal">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dewey Decimal</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.DeweyDecimal }}</p>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Copies</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.Copies || 1 }}</p>
              </div>
              <div *ngIf="selectedBook.ShelfLocation">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Shelf Location</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.ShelfLocation }}</p>
              </div>
            </div>

            <div *ngIf="selectedBook.AcquisitionDate">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Acquisition Date</label>
              <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.AcquisitionDate | date:'mediumDate' }}</p>
            </div>
          </div>

          <div class="md:col-span-2" *ngIf="selectedBook.Remarks">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Remarks</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ selectedBook.Remarks }}</p>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
        <button
          (click)="closeViewBookModal()"
          class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
          Close
        </button>
        <button
          (click)="editBook(selectedBook.BookID)"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
          Edit Book
        </button>
      </div>
    </div>
  </div>

  <!-- Edit Book Modal -->
  <div *ngIf="showEditBookModal && selectedBook" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Edit Book</h3>
          <button (click)="closeEditBookModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <form (ngSubmit)="onUpdateBook()" #editBookForm="ngForm" class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Title -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              [(ngModel)]="editBookData.Title"
              name="editTitle"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              [class.border-red-500]="!editBookData.Title && isSubmitting"
              [class.dark:border-red-500]="!editBookData.Title && isSubmitting"
              placeholder="Enter book title">
            <p *ngIf="!editBookData.Title && isSubmitting" class="text-red-500 text-xs mt-1">Title is required</p>
          </div>

          <!-- Author -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Author</label>
            <input
              type="text"
              [(ngModel)]="editBookData.Author"
              name="editAuthor"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter author name">
          </div>

          <!-- ISBN -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              ISBN <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              [(ngModel)]="editBookData.ISBN"
              name="editIsbn"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              [class.border-red-500]="!editBookData.ISBN && isSubmitting"
              [class.dark:border-red-500]="!editBookData.ISBN && isSubmitting"
              placeholder="Enter ISBN">
            <p *ngIf="!editBookData.ISBN && isSubmitting" class="text-red-500 text-xs mt-1">ISBN is required</p>
          </div>

          <!-- Category -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
            <select
              [(ngModel)]="editBookData.Category"
              name="editCategory"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="">Select Category</option>
              <option value="Computer Science">Computer Science</option>
              <option value="Mathematics">Mathematics</option>
              <option value="History">History</option>
              <option value="Literature">Literature</option>
              <option value="Science">Science</option>
              <option value="Engineering">Engineering</option>
              <option value="Business">Business</option>
              <option value="Arts">Arts</option>
              <option value="Philosophy">Philosophy</option>
              <option value="Religion">Religion</option>
              <option value="Others">Others</option>
              <option value="Miscellaneous">Miscellaneous</option>
            </select>
          </div>

          <!-- Subject -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Subject</label>
            <input
              type="text"
              [(ngModel)]="editBookData.Subject"
              name="editSubject"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter subject">
          </div>

          <!-- Published Year -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Published Year</label>
            <input
              type="number"
              [(ngModel)]="editBookData.PublishedYear"
              name="editPublishedYear"
              min="1000"
              max="2030"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter published year">
          </div>

          <!-- Copyright Year -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Copyright Year</label>
            <input
              type="number"
              [(ngModel)]="editBookData.CopyrightYear"
              name="editCopyrightYear"
              min="1000"
              max="2030"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter copyright year">
          </div>

          <!-- Publisher -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Publisher</label>
            <input
              type="text"
              [(ngModel)]="editBookData.Publisher"
              name="editPublisher"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter publisher">
          </div>

          <!-- Call Number -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Call Number</label>
            <input
              type="text"
              [(ngModel)]="editBookData.CallNumber"
              name="editCallNumber"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter call number">
          </div>

          <!-- Dewey Decimal -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Dewey Decimal</label>
            <input
              type="text"
              [(ngModel)]="editBookData.DeweyDecimal"
              name="editDeweyDecimal"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter Dewey Decimal">
          </div>

          <!-- Copies -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Number of Copies</label>
            <input
              type="number"
              [(ngModel)]="editBookData.Copies"
              name="editCopies"
              min="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter number of copies">
          </div>

          <!-- Status -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
            <select
              [(ngModel)]="editBookData.Status"
              name="editStatus"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="Available">Available</option>
              <option value="Borrowed">Borrowed</option>
              <option value="Lost">Lost</option>
              <option value="Damaged">Damaged</option>
            </select>
          </div>

          <!-- Shelf Location -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Shelf Location</label>
            <input
              type="text"
              [(ngModel)]="editBookData.ShelfLocation"
              name="editShelfLocation"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter shelf location">
          </div>

          <!-- Acquisition Date -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Acquisition Date</label>
            <input
              type="date"
              [(ngModel)]="editBookData.AcquisitionDate"
              name="editAcquisitionDate"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Remarks -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Remarks</label>
            <textarea
              [(ngModel)]="editBookData.Remarks"
              name="editRemarks"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter any remarks or notes about the book"></textarea>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            (click)="closeEditBookModal()"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
            Cancel
          </button>
          <button
            type="button"
            (click)="onUpdateBook()"
            [disabled]="!editBookData.Title || !editBookData.ISBN || isSubmitting"
            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center">
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? 'Updating...' : 'Update Book' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div *ngIf="showDeleteConfirmModal && selectedBook" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Confirm Delete</h3>
          <button (click)="closeDeleteConfirmModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-medium text-gray-900 dark:text-white">Delete Book</h4>
            <p class="text-sm text-gray-500 dark:text-gray-400">This action cannot be undone.</p>
          </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
          <h5 class="font-medium text-gray-900 dark:text-white mb-2">{{ selectedBook.Title }}</h5>
          <p class="text-sm text-gray-600 dark:text-gray-400" *ngIf="selectedBook.Author">by {{ selectedBook.Author }}</p>
          <p class="text-xs text-gray-500 dark:text-gray-500" *ngIf="selectedBook.ISBN">ISBN: {{ selectedBook.ISBN }}</p>
        </div>

        <p class="text-sm text-gray-600 dark:text-gray-400">
          Are you sure you want to delete this book? This will permanently remove it from the library catalog.
        </p>
      </div>

      <!-- Modal Footer -->
      <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
        <button
          (click)="closeDeleteConfirmModal()"
          [disabled]="isSubmitting"
          class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors">
          Cancel
        </button>
        <button
          (click)="confirmDelete()"
          [disabled]="isSubmitting"
          class="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center">
          <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isSubmitting ? 'Deleting...' : 'Delete Book' }}
        </button>
      </div>
    </div>
  </div>

  <!-- Notification Modal -->
  <div *ngIf="showNotification" class="fixed top-4 right-4 z-50 max-w-sm w-full">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border-l-4 p-4 transition-all duration-300 ease-in-out"
         [ngClass]="{
           'border-green-500': notificationType === 'success',
           'border-red-500': notificationType === 'error'
         }">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <!-- Success Icon -->
          <svg *ngIf="notificationType === 'success'" class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <!-- Error Icon -->
          <svg *ngIf="notificationType === 'error'" class="w-6 h-6 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ notificationType === 'success' ? 'Success' : 'Error' }}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ notificationMessage }}</p>
          </div>
        </div>
        <button
          (click)="hideNotification()"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
