<!-- Dashboard Overview Content -->
<div class="max-w-7xl mx-auto">
  <!-- Latest News Section -->
  <div class="mb-6 lg:mb-8 news-section">
    <h3 class="text-lg lg:text-xl font-semibold mb-3 lg:mb-4" [class]="getTextClasses()">Latest News</h3>
    <div class="space-y-3">
      <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
          <p [class]="getSecondaryTextClasses()">Library closed on July 12.</p>
        </div>
      </div>
      <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
          <p [class]="getSecondaryTextClasses()">New Science books available!</p>
        </div>
      </div>
      <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
          <p [class]="getSecondaryTextClasses()">Join the Reading Challenge.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Posts & Announcements Section -->
  <div class="mb-6 lg:mb-8 announcements-section">
    <h3 class="text-lg lg:text-xl font-semibold mb-3 lg:mb-4" [class]="getTextClasses()">Posts & Announcements</h3>
    <div class="p-6 rounded-lg shadow-sm border transition-shadow duration-200" [class]="getCardClasses()">
      <div class="flex items-center">
        <div class="flex-shrink-0 mr-4">
          <svg class="w-8 h-8 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        </div>
        <div class="flex-1">
          <p class="text-lg font-medium" [class]="getTextClasses()">Return books by July 10 to avoid fees.</p>
          <p class="text-sm mt-1" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Posted 2 hours ago</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Widgets Section - Only visible on mobile -->
  <div class="lg:hidden space-y-6">
    <!-- Weather Widget -->
    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="text-lg font-semibold mb-1">Weather</h4>
          <p class="text-3xl font-bold" id="temperature-mobile">31°C</p>
          <p class="text-blue-100" id="location-mobile">Cebu City</p>
        </div>
        <div class="text-right">
          <svg class="w-12 h-12 text-yellow-300" fill="currentColor" viewBox="0 0 24 24" id="weather-icon-mobile">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Horoscope Widget -->
    <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
      <div class="flex items-center mb-3">
        <svg class="w-6 h-6 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
        <h4 class="text-lg font-semibold" [class]="getTextClasses()">Horoscope</h4>
      </div>
      <p [class]="getSecondaryTextClasses()"><strong>Cancer:</strong> Study focus brings good results.</p>
    </div>

    <!-- Calendar Events Widget -->
    <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
      <div class="flex items-center mb-4">
        <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <h4 class="text-lg font-semibold" [class]="getTextClasses()">Upcoming Events</h4>
      </div>
      <div class="space-y-3">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <div>
            <p class="font-medium" [class]="getTextClasses()">July 10 – Book Fair</p>
            <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">3 days remaining</p>
          </div>
        </div>
        <div class="flex items-center">
          <svg class="w-5 h-5 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <div>
            <p class="font-medium" [class]="getTextClasses()">July 15 – Research Submission</p>
            <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">8 days remaining</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quote of the Day Widget -->
    <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
      <div class="flex items-center mb-3">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
        </svg>
        <h4 class="text-lg font-semibold">Quote of the Day</h4>
      </div>
      <p class="text-purple-100 italic">"A room without books is like a body without a soul."</p>
    </div>

    <!-- School Stats Widget -->
    <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
      <h4 class="text-lg font-semibold mb-4" [class]="getTextClasses()">School Stats</h4>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            <span [class]="getSecondaryTextClasses()">Books</span>
          </div>
          <span class="text-xl font-bold" [class]="getTextClasses()" id="books-count-mobile">3,456</span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span [class]="getSecondaryTextClasses()">Members</span>
          </div>
          <span class="text-xl font-bold" [class]="getTextClasses()" id="members-count-mobile">1,230</span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <span [class]="getSecondaryTextClasses()">Active Today</span>
          </div>
          <span class="text-xl font-bold" [class]="getTextClasses()" id="active-count-mobile">87</span>
        </div>
      </div>
    </div>
  </div>
</div>
