import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Faculty {
  employeeId: string;
  name: string;
  department: string;
  position: string;
  email: string;
  joinDate: string;
  status: 'Active' | 'Inactive' | 'Pending';
}

interface FacultyStats {
  totalFaculty: number;
  activeFaculty: number;
  departments: number;
  pendingApprovals: number;
}

@Component({
  selector: 'app-faculty',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './faculty.component.html',
  styleUrls: ['./faculty.component.css']
})
export class FacultyComponent implements OnInit {
  isDarkMode: boolean = false;

  stats: FacultyStats = {
    totalFaculty: 48,
    activeFaculty: 45,
    departments: 8,
    pendingApprovals: 3
  };

  facultyMembers: Faculty[] = [
    {
      employeeId: 'F2024001',
      name: 'Dr. <PERSON>',
      department: 'Computer Science',
      position: 'Professor',
      email: '<EMAIL>',
      joinDate: '2020-08-15',
      status: 'Active'
    },
    {
      employeeId: 'F2024002',
      name: 'Prof. <PERSON>',
      department: 'Mathematics',
      position: 'Associate Professor',
      email: '<EMAIL>',
      joinDate: '2019-06-10',
      status: 'Active'
    },
    {
      employeeId: 'F2024003',
      name: 'Dr. Ana Lucia Fernandez',
      department: 'English',
      position: 'Department Head',
      email: '<EMAIL>',
      joinDate: '2018-03-20',
      status: 'Active'
    },
    {
      employeeId: 'F2024004',
      name: 'Prof. <PERSON> <PERSON> Torres',
      department: 'Science',
      position: 'Assistant Professor',
      email: '<EMAIL>',
      joinDate: '2021-01-12',
      status: 'Active'
    },
    {
      employeeId: 'F2024005',
      name: 'Dr. Carmen Isabella Reyes',
      department: 'Psychology',
      position: 'Professor',
      email: '<EMAIL>',
      joinDate: '2017-09-05',
      status: 'Inactive'
    },
    {
      employeeId: 'F2024006',
      name: 'Prof. Diego Antonio Morales',
      department: 'History',
      position: 'Associate Professor',
      email: '<EMAIL>',
      joinDate: '2022-02-28',
      status: 'Active'
    },
    {
      employeeId: 'F2024007',
      name: 'Dr. Sofia Gabriela Cruz',
      department: 'Biology',
      position: 'Research Professor',
      email: '<EMAIL>',
      joinDate: '2024-07-01',
      status: 'Pending'
    },
    {
      employeeId: 'F2024008',
      name: 'Prof. Luis Fernando Mendoza',
      department: 'Physics',
      position: 'Assistant Professor',
      email: '<EMAIL>',
      joinDate: '2023-11-15',
      status: 'Active'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    this.isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
  }

  getTextClasses(): string {
    return this.isDarkMode ? 'text-white' : 'text-gray-900';
  }

  getSecondaryTextClasses(): string {
    return this.isDarkMode ? 'text-gray-400' : 'text-gray-600';
  }

  getCardClasses(): string {
    return this.isDarkMode 
      ? 'bg-gray-800 border-gray-700 text-white' 
      : 'bg-white border-gray-200 text-gray-900';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Inactive':
        return 'bg-red-100 text-red-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('');
  }
}
