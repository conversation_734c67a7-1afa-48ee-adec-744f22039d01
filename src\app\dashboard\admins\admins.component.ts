import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../services/theme.service';

@Component({
  selector: 'app-admins',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './admins.component.html',
  styleUrls: ['./admins.component.css']
})
export class AdminsComponent implements OnInit {

  admins = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Super Admin',
      department: 'IT Department',
      lastLogin: '2 hours ago',
      status: 'Active'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Li<PERSON>rian',
      department: 'Library Services',
      lastLogin: '1 day ago',
      status: 'Active'
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Admin',
      department: 'Academic Affairs',
      lastLogin: '3 days ago',
      status: 'Inactive'
    }
  ];

  constructor(private themeService: ThemeService) { }

  // Getter for dark mode state from theme service
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode;
  }

  ngOnInit(): void {
    // Component initialization
  }

  addNewAdmin(): void {
    console.log('Add new admin clicked');
  }

  editAdmin(adminId: number): void {
    console.log('Edit admin:', adminId);
  }

  viewAdmin(adminId: number): void {
    console.log('View admin:', adminId);
  }

  managePermissions(adminId: number): void {
    console.log('Manage permissions for admin:', adminId);
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  getRoleColor(role: string): string {
    switch (role) {
      case 'Super Admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'Admin':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'Librarian':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }
}
