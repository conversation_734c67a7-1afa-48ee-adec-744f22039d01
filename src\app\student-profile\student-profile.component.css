/* Student Profile Component Styles */

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Profile photo hover effects */
.profile-photo-container:hover .edit-photo-overlay {
  opacity: 1;
}

.edit-photo-overlay {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

/* Form input focus styles */
input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button hover animations */
button {
  transform: translateY(0);
  transition: all 0.2s ease-in-out;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Card hover effects */
.profile-card {
  transition: all 0.3s ease-in-out;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Dark mode specific styles */
[data-theme="dark"] .profile-card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .profile-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

/* Responsive design adjustments */
@media (max-width: 768px) {
  .side-panel {
    width: 100%;
    max-width: 320px;
  }
  
  .main-content {
    margin-left: 0;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}

/* Profile photo upload styles */
.profile-photo-upload {
  position: relative;
  display: inline-block;
}

.profile-photo-upload input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* Form validation styles */
.form-error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success message styles */
.success-message {
  color: #10b981;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Disabled input styles */
input:disabled,
input[readonly] {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Focus ring for accessibility */
button:focus,
input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }
  
  button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .profile-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

/* Upload Modal Animations */
.upload-modal-enter {
  animation: modalFadeIn 0.3s ease-out;
}

.upload-modal-leave {
  animation: modalFadeOut 0.3s ease-in;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
}

/* Progress bar animation */
.progress-bar {
  transition: width 0.3s ease-out;
}

/* Upload success animation */
.upload-success {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Spinner animation */
.upload-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Error shake animation */
.upload-error {
  animation: errorShake 0.5s ease-out;
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Modal backdrop blur effect */
.modal-backdrop {
  backdrop-filter: blur(4px);
  transition: backdrop-filter 0.3s ease-out;
}

/* File info card hover effect */
.file-info-card {
  transition: all 0.2s ease-out;
}

.file-info-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
