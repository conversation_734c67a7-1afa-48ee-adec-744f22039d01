<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Forgot Password - {{ getUserTypeDisplay() }} | Benedicto College Library</title>
  <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body class="bg-gradient-to-br from-blue-50 via-white to-orange-50 min-h-screen flex flex-col">

  <!-- Enhanced Professional Header with Hover Text Labels -->
  <header class="bg-gray-950 shadow-xl relative z-10 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Desktop Navigation - Right aligned with SVG icons only -->
        <nav class="hidden md:flex items-center space-x-1 lg:space-x-2 flex-shrink-0">
          <!-- About -->
          <div class="relative group">
            <a routerLink="/about" class="p-2 lg:p-3 text-white hover:bg-gray-800 rounded-lg transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 lg:w-6 lg:h-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
              About
            </div>
          </div>

          <!-- Contact -->
          <div class="relative group">
            <a routerLink="/contact" class="p-2 lg:p-3 text-white hover:bg-gray-800 rounded-lg transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 lg:w-6 lg:h-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
              Contact
            </div>
          </div>

          <!-- Support -->
          <div class="relative group">
            <a routerLink="/support" class="p-2 lg:p-3 text-white hover:bg-gray-800 rounded-lg transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 lg:w-6 lg:h-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                <path d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"/>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
              Help & Support
            </div>
          </div>

          <!-- Website -->
          <div class="relative group">
            <a href="https://benedictocollege.edu.ph/" target="_blank" class="p-2 lg:p-3 text-white hover:bg-gray-800 rounded-lg transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 lg:w-6 lg:h-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
              Official Website
            </div>
          </div>
        </nav>

        <!-- Mobile menu button -->
        <button class="md:hidden p-2 text-white hover:bg-gray-800 rounded-lg transition duration-300" (click)="toggleMobileMenu()">
          <svg class="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Modal -->
    <div *ngIf="isMobileMenuOpen" class="fixed inset-0 z-50 md:hidden">
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-black bg-opacity-50 mobile-menu-overlay" (click)="toggleMobileMenu()"></div>

      <!-- Sidebar -->
      <div class="fixed top-0 right-0 h-full w-80 max-w-[50vw] bg-gray-900 shadow-xl transform transition-transform duration-300 ease-in-out">
        <div class="flex flex-col h-full">
          <!-- Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-700">
            <img src="assets/images/BcLogo.png" alt="Benedicto College" class="h-8 w-auto">
            <button (click)="toggleMobileMenu()" class="p-2 text-white hover:bg-gray-800 rounded-lg transition duration-300">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Navigation Links -->
          <nav class="flex-1 px-4 py-6 space-y-4">
            <a routerLink="/about" (click)="toggleMobileMenu()" class="flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300">
              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
              </svg>
              <span>About</span>
            </a>

            <a routerLink="/contact" (click)="toggleMobileMenu()" class="flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300">
              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
              </svg>
              <span>Contact</span>
            </a>

            <a routerLink="/support" (click)="toggleMobileMenu()" class="flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300">
              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"/>
              </svg>
              <span>Help & Support</span>
            </a>

            <a href="https://benedictocollege.edu.ph/" target="_blank" (click)="toggleMobileMenu()" class="flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300">
              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span>Official Website</span>
            </a>
          </nav>

          <!-- Footer -->
          <div class="p-4 border-t border-gray-700">
            <div class="text-center">
              <p class="text-gray-400 text-xs">© 2024 Benedicto College</p>
              <p class="text-gray-500 text-xs mt-1">Library Management System</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- main content -->
  <main class="flex-1 flex">

    <!-- logo image -->
    <div class="flex items-center justify-center p-7">
      <img
        src="assets/images/login-logo.png"
        alt="Benedicto College {{ getUserTypeDisplay() }} Password Reset"
        class="max-w-full h-auto object-fit-contain"
        style="max-height: 470px;"
        onerror="this.src='assets/images/login-logo.jpg'; console.log('Switched to JPG format');"
        onload="console.log('Password reset logo loaded successfully');"
      >
    </div>

    <!-- forgot password form container -->
    <div class="flex items-center justify-center p-4 sm:p-5 lg:p-6">
      <!-- forgot password card -->
      <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-4 sm:p-5 w-full max-w-sm lg:max-w-md mx-auto relative overflow-hidden">
        <!-- decorative circles -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-{{getThemeColors().primary}}/20 to-{{getThemeColors().primary}}/10 rounded-full opacity-60 -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-orange-200/40 to-orange-300/30 rounded-full opacity-60 translate-y-12 -translate-x-12"></div>

        <!-- form content -->
        <div class="relative z-10">
          <!-- header -->
          <div class="text-center mb-6">
            <h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
              Reset Password
            </h2>
            <p class="text-sm text-gray-600">
              {{ getUserTypeDisplay() }} Account Recovery
            </p>
          </div>

          <!-- Step indicator -->
          <div class="flex justify-center mb-6">
            <div class="flex items-center space-x-2 sm:space-x-4 overflow-x-auto">
              <!-- Step 1: Email -->
              <div class="flex items-center flex-shrink-0">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     [class]="getStepClasses('email', currentStep === 'email')">
                  1
                </div>
                <span class="ml-2 text-xs sm:text-sm font-medium hidden sm:block"
                      [class]="getStepTextClasses('email', currentStep === 'email')">
                  Email
                </span>
              </div>
              <div class="w-4 sm:w-8 h-0.5 bg-gray-200 flex-shrink-0"></div>

              <!-- Step 2: OTP -->
              <div class="flex items-center flex-shrink-0">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     [class]="getStepClasses('otp', currentStep === 'otp')">
                  2
                </div>
                <span class="ml-2 text-xs sm:text-sm font-medium hidden sm:block"
                      [class]="getStepTextClasses('otp', currentStep === 'otp')">
                  Verify OTP
                </span>
              </div>
              <div class="w-4 sm:w-8 h-0.5 bg-gray-200 flex-shrink-0"></div>

              <!-- Step 3: Reset -->
              <div class="flex items-center flex-shrink-0">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     [class]="getStepClasses('reset', currentStep === 'reset')">
                  3
                </div>
                <span class="ml-2 text-xs sm:text-sm font-medium hidden sm:block"
                      [class]="getStepTextClasses('reset', currentStep === 'reset')">
                  New Password
                </span>
              </div>
              <div class="w-4 sm:w-8 h-0.5 bg-gray-200 flex-shrink-0"></div>

              <!-- Step 4: Manual -->
              <div class="flex items-center flex-shrink-0">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     [class]="getStepClasses('form', currentStep === 'form')">
                  4
                </div>
                <span class="ml-2 text-xs sm:text-sm font-medium hidden sm:block"
                      [class]="getStepTextClasses('form', currentStep === 'form')">
                  Manual Request
                </span>
              </div>
            </div>
          </div>

          <!-- Email Step -->
          <div *ngIf="currentStep === 'email' && !emailSuccess">
            <form (ngSubmit)="onEmailSubmit($event)" class="space-y-4">
              <div class="text-center mb-4">
                <p class="text-sm text-gray-600">
                  Enter your email address and we'll send you a password reset link.
                </p>
              </div>

              <div>
                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                  Email Address <span class="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  [(ngModel)]="email"
                  placeholder="Enter your email address"
                  autocomplete="email"
                  (input)="clearEmailError()"
                  [class]="getInputFocusClasses() + ' sm:px-4 sm:py-2.5 sm:text-base'"
                  [disabled]="isEmailLoading"
                >
              </div>

              <!-- Email Error Message -->
              <div *ngIf="showEmailError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium bg-red-50 border border-red-200 rounded-lg p-2">
                {{ emailError }}
              </div>

              <!-- Submit button -->
              <button
                type="submit"
                [disabled]="isEmailLoading"
                [class]="getButtonClasses() + ' sm:py-3 sm:text-base'"
              >
                <span *ngIf="!isEmailLoading">Send Reset Link</span>
                <span *ngIf="isEmailLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Sending...
                </span>
              </button>

              <!-- Alternative option -->
              <div class="text-center mt-4">
                <p class="text-sm text-gray-600 mb-2">Can't access your email?</p>
                <button
                  type="button"
                  (click)="switchToForm()"
                  [class]="getLinkClasses()"
                >
                  Fill out manual request form
                </button>
              </div>
            </form>
          </div>

          <!-- OTP Verification Step -->
          <div *ngIf="currentStep === 'otp'">
            <form (ngSubmit)="onOTPSubmit($event)" class="space-y-4">
              <div class="text-center mb-4">
                <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Verify Your Email</h3>
                <p class="text-sm text-gray-600 mb-2">
                  We've sent a 6-digit verification code to:
                </p>
                <p class="text-sm font-semibold text-gray-900 mb-2">{{ email }}</p>
                <p class="text-xs text-gray-500">
                  Code expires in {{ otpExpiryMinutes }} minutes
                </p>
              </div>

              <div>
                <label for="otp" class="block text-sm font-semibold text-gray-700 mb-2">
                  Verification Code <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="otp"
                  name="otp"
                  [(ngModel)]="otp"
                  placeholder="Enter 6-digit code"
                  maxlength="6"
                  autocomplete="one-time-code"
                  (input)="clearOTPError()"
                  [class]="getInputFocusClasses() + ' sm:px-4 sm:py-2.5 sm:text-base text-center text-lg tracking-widest font-mono'"
                  [disabled]="isOTPLoading"
                >
              </div>

              <!-- OTP Error Message -->
              <div *ngIf="showOTPError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium bg-red-50 border border-red-200 rounded-lg p-2">
                {{ otpError }}
              </div>

              <!-- Submit button -->
              <button
                type="submit"
                [disabled]="isOTPLoading"
                [class]="getButtonClasses() + ' sm:py-3 sm:text-base'"
              >
                <span *ngIf="!isOTPLoading">Verify Code</span>
                <span *ngIf="isOTPLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </span>
              </button>

              <!-- Navigation options -->
              <div class="flex flex-col sm:flex-row gap-2 sm:gap-4 text-center">
                <button
                  type="button"
                  (click)="backToEmail()"
                  [class]="getLinkClasses()"
                  class="flex-1"
                >
                  ← Back to Email
                </button>
                <button
                  type="button"
                  (click)="resendOTP()"
                  [class]="getLinkClasses()"
                  class="flex-1"
                >
                  Resend Code
                </button>
              </div>
            </form>
          </div>

          <!-- Password Reset Step -->
          <div *ngIf="currentStep === 'reset' && !resetSuccess">
            <form (ngSubmit)="onPasswordResetSubmit($event)" class="space-y-4">
              <div class="text-center mb-4">
                <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Create New Password</h3>
                <p class="text-sm text-gray-600">
                  Please enter your new password below.
                </p>
              </div>

              <!-- New Password -->
              <div>
                <label for="newPassword" class="block text-sm font-semibold text-gray-700 mb-2">
                  New Password <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <input
                    [type]="showNewPassword ? 'text' : 'password'"
                    id="newPassword"
                    name="newPassword"
                    [(ngModel)]="newPassword"
                    placeholder="Enter new password"
                    autocomplete="new-password"
                    (input)="clearResetError()"
                    [class]="getInputFocusClasses() + ' sm:px-4 sm:py-2.5 sm:text-base pr-12'"
                    [disabled]="isResetLoading"
                  >
                  <button
                    type="button"
                    (click)="toggleNewPasswordVisibility()"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    [disabled]="isResetLoading"
                  >
                    <svg *ngIf="!showNewPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <svg *ngIf="showNewPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  </button>
                </div>
                <p class="text-xs text-gray-500 mt-1">
                  Must be at least 6 characters with uppercase, lowercase, and number
                </p>
              </div>

              <!-- Confirm Password -->
              <div>
                <label for="confirmPassword" class="block text-sm font-semibold text-gray-700 mb-2">
                  Confirm Password <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <input
                    [type]="showConfirmPassword ? 'text' : 'password'"
                    id="confirmPassword"
                    name="confirmPassword"
                    [(ngModel)]="confirmPassword"
                    placeholder="Confirm new password"
                    autocomplete="new-password"
                    (input)="clearResetError()"
                    [class]="getInputFocusClasses() + ' sm:px-4 sm:py-2.5 sm:text-base pr-12'"
                    [disabled]="isResetLoading"
                  >
                  <button
                    type="button"
                    (click)="toggleConfirmPasswordVisibility()"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    [disabled]="isResetLoading"
                  >
                    <svg *ngIf="!showConfirmPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <svg *ngIf="showConfirmPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Reset Error Message -->
              <div *ngIf="showResetError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium bg-red-50 border border-red-200 rounded-lg p-2">
                {{ resetError }}
              </div>

              <!-- Submit button -->
              <button
                type="submit"
                [disabled]="isResetLoading"
                [class]="getButtonClasses() + ' sm:py-3 sm:text-base'"
              >
                <span *ngIf="!isResetLoading">Reset Password</span>
                <span *ngIf="isResetLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Resetting...
                </span>
              </button>
            </form>
          </div>

          <!-- Password Reset Success -->
          <div *ngIf="currentStep === 'reset' && resetSuccess" class="text-center py-8">
            <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Password Reset Successful!</h3>
            <p class="text-sm text-gray-600 mb-4">
              Your password has been successfully updated. You can now log in with your new password.
            </p>
            <p class="text-xs text-gray-500">
              Redirecting to login page in a few seconds...
            </p>
          </div>

          <!-- Manual Form Step -->
          <div *ngIf="currentStep === 'form' && !formSuccess">
            <form (ngSubmit)="onFormSubmit($event)" class="space-y-4">
              <div class="text-center mb-4">
                <p class="text-sm text-gray-600">
                  Fill out this form and the librarian will email your password reset information.
                </p>
              </div>

              <!-- Name fields -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">
                    First Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    [(ngModel)]="firstName"
                    placeholder="Enter first name"
                    autocomplete="given-name"
                    (input)="clearFormError()"
                    [class]="getInputFocusClasses()"
                    [disabled]="isFormLoading"
                  >
                </div>

                <div>
                  <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">
                    Last Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    [(ngModel)]="lastName"
                    placeholder="Enter last name"
                    autocomplete="family-name"
                    (input)="clearFormError()"
                    [class]="getInputFocusClasses()"
                    [disabled]="isFormLoading"
                  >
                </div>
              </div>

              <!-- Student-specific fields -->
              <div *ngIf="userType === 'student'">
                <div>
                  <label for="studentId" class="block text-sm font-semibold text-gray-700 mb-2">
                    Student ID <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="studentId"
                    name="studentId"
                    [(ngModel)]="studentId"
                    placeholder="2000-00000"
                    maxlength="10"
                    inputmode="numeric"
                    (input)="onStudentIdInput($event); clearFormError()"
                    (keydown)="onStudentIdKeydown($event)"
                    [class]="getInputFocusClasses()"
                    [disabled]="isFormLoading"
                  >
                </div>

                <div>
                  <label for="course" class="block text-sm font-semibold text-gray-700 mb-2">
                    Course <span class="text-red-500">*</span>
                  </label>
                  <select
                    id="course"
                    name="course"
                    [(ngModel)]="course"
                    (change)="clearFormError()"
                    [class]="getInputFocusClasses()"
                    [disabled]="isFormLoading"
                  >
                    <option value="">Select your course</option>
                    <option *ngFor="let courseOption of courses" [value]="courseOption">
                      {{ courseOption }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- Email field -->
              <div>
                <label for="formEmail" class="block text-sm font-semibold text-gray-700 mb-2">
                  Email Address <span class="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="formEmail"
                  name="formEmail"
                  [(ngModel)]="formEmail"
                  placeholder="Enter your email address"
                  autocomplete="email"
                  (input)="clearFormError()"
                  [class]="getInputFocusClasses()"
                  [disabled]="isFormLoading"
                >
              </div>

              <!-- Phone number field -->
              <div>
                <label for="phoneNumber" class="block text-sm font-semibold text-gray-700 mb-2">
                  Phone Number <span class="text-gray-500">(Optional)</span>
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  [(ngModel)]="phoneNumber"
                  placeholder="Enter your phone number"
                  autocomplete="tel"
                  (input)="clearFormError()"
                  [class]="getInputFocusClasses()"
                  [disabled]="isFormLoading"
                >
              </div>

              <!-- Form Error Message -->
              <div *ngIf="showFormError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium bg-red-50 border border-red-200 rounded-lg p-2">
                {{ formError }}
              </div>

              <!-- Submit button -->
              <button
                type="submit"
                [disabled]="isFormLoading"
                [class]="getButtonClasses()"
              >
                <span *ngIf="!isFormLoading">Submit Request</span>
                <span *ngIf="isFormLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </span>
              </button>

              <!-- Back to email option -->
              <div class="text-center mt-4">
                <button
                  type="button"
                  (click)="switchToEmail()"
                  [class]="getLinkClasses()"
                >
                  ← Back to email reset
                </button>
              </div>
            </form>
          </div>

          <!-- Form Success Message -->
          <div *ngIf="formSuccess" class="text-center">
            <div class="mb-4">
              <svg class="w-16 h-16 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Request Submitted!</h3>
            <p class="text-sm text-gray-600 mb-4">
              Your password reset request has been submitted to the librarian. You will receive an email with your new password within 24 hours.
            </p>
            <p class="text-xs text-gray-500">
              Redirecting to login page in a few seconds...
            </p>
          </div>

          <!-- Back to login link -->
          <div *ngIf="!emailSuccess && !formSuccess" class="text-center mt-6 pt-4 border-t border-gray-200">
            <a [routerLink]="getLoginRoute()"
               [class]="getBackLinkClasses()">
              ← Back to {{ getUserTypeDisplay() }} Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>
</body>
</html>
