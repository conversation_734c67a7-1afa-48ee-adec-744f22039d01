<!-- Professional Profile Page with Side Navigation -->
<div class="min-h-screen" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-100'">
  <div class="flex min-h-screen">

    <!-- Side Profile Panel - Fixed Width -->
    <div class="w-80 flex-none border-r overflow-y-auto" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
      <!-- Header Section -->
      <div class="relative p-6 pb-8" [class]="isDarkMode ? 'bg-gray-800' : 'bg-gray-50'">

        <!-- Profile Photo Section -->
        <div class="text-center mb-6">
          <div class="relative inline-block">
            <div class="w-28 h-28 rounded-full overflow-hidden shadow-lg">
              <img
                [src]="getSafeImageUrl()"
                [alt]="profileData.firstName + ' ' + profileData.lastName"
                class="w-full h-full object-cover"
                [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-100'"
                crossorigin="anonymous"
                (error)="onImageError($event)"
                (load)="onImageLoad($event)"
              >
            </div>
            <!-- Edit Photo Button -->
            <label
              *ngIf="isEditing"
              for="fileInput"
              class="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors duration-200 cursor-pointer shadow-lg"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </label>
            <!-- Hidden File Input -->
            <input
              id="fileInput"
              type="file"
              accept="image/*"
              (change)="onFileSelected($event)"
              class="hidden"
            >
          </div>

          <!-- User Name -->
          <div class="mt-4">
            <h2 class="text-xl font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">
              {{ profileData.firstName }} {{ profileData.lastName }}
            </h2>
            <p class="text-sm font-medium text-blue-600 mt-1">
              {{ profileData.role || 'Library Member' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="p-6">

        <!-- Personal Information -->
        <div class="mb-8">
          <div class="flex items-center space-x-3 mb-4 pb-2 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
            <div class="w-6 h-6 rounded-lg flex items-center justify-center" [class]="isDarkMode ? 'bg-blue-600' : 'bg-blue-100'">
              <svg class="w-4 h-4" [class]="isDarkMode ? 'text-white' : 'text-blue-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <h3 class="text-base font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Personal Details</h3>
          </div>

          <!-- Name Details -->
          <div class="space-y-4">
            <div>
              <label class="text-xs font-medium uppercase tracking-wide" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">First Name</label>
              <div class="mt-1 p-3 rounded-lg border" [class]="isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.firstName || 'N/A' }}
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">Last Name</label>
              <div class="mt-1 p-3 rounded-lg border" [class]="isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.lastName || 'N/A' }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div>
          <div class="flex items-center space-x-3 mb-4 pb-2 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
            <div class="w-6 h-6 rounded-lg flex items-center justify-center" [class]="isDarkMode ? 'bg-green-600' : 'bg-green-100'">
              <svg class="w-4 h-4" [class]="isDarkMode ? 'text-white' : 'text-green-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-base font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Contact Information</h3>
          </div>

          <div class="space-y-4">
            <div>
              <label class="text-xs font-medium uppercase tracking-wide" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">Email</label>
              <div class="mt-1 p-3 rounded-lg border" [class]="isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.email || 'N/A' }}
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">Phone Number</label>
              <div class="mt-1 p-3 rounded-lg border" [class]="isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.phoneNumber || 'N/A' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 min-w-0 p-6 overflow-y-auto">
      <!-- Header Section -->
      <div class="flex items-center justify-between mb-6">
        <!-- Title -->
        <div>
          <h1 class="text-2xl font-bold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Profile Settings</h1>
          <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">
            Manage your account information and preferences
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center space-x-3">
          <button
            (click)="goBack()"
            class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
            [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
          >
            Back to Dashboard
          </button>
          <button
            *ngIf="!isEditing"
            (click)="toggleEdit()"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200"
          >
            Edit Profile
          </button>

          <!-- Edit Mode Buttons -->
          <div *ngIf="isEditing" class="flex items-center space-x-3">
            <button
              (click)="toggleEdit()"
              class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
              [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
              [disabled]="isSaving"
            >
              Cancel
            </button>
            <button
              (click)="saveProfile()"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 disabled:opacity-50"
              [disabled]="isSaving"
            >
              {{ isSaving ? 'Saving...' : 'Save Changes' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex items-center justify-center py-12">
        <div class="flex items-center space-x-3">
          <svg class="w-6 h-6 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-600'">Loading profile...</span>
        </div>
      </div>

      <!-- Main Content -->
      <div *ngIf="!isLoading" class="space-y-4">

        <!-- Account Information Card -->
        <div class="rounded-lg shadow-sm border" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
          <div class="px-6 py-3 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
            <h2 class="text-lg font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Account Information</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- First Name -->
              <div>
                <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">First Name</label>
                <input
                  type="text"
                  [(ngModel)]="profileData.firstName"
                  name="firstName"
                  [readonly]="!isEditing"
                  class="w-full px-3 py-2 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  [class]="isEditing ?
                    (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                    (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                  placeholder="Enter first name"
                >
              </div>

              <!-- Last Name -->
              <div>
                <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Last Name</label>
                <input
                  type="text"
                  [(ngModel)]="profileData.lastName"
                  name="lastName"
                  [readonly]="!isEditing"
                  class="w-full px-3 py-2 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  [class]="isEditing ?
                    (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                    (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                  placeholder="Enter last name"
                >
              </div>

              <!-- Email -->
              <div>
                <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Email Address</label>
                <input
                  type="email"
                  [(ngModel)]="profileData.email"
                  name="email"
                  [readonly]="!isEditing"
                  class="w-full px-3 py-2 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  [class]="isEditing ?
                    (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                    (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                  placeholder="Enter email address"
                >
              </div>

              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Phone Number</label>
                <input
                  type="tel"
                  [(ngModel)]="profileData.phoneNumber"
                  name="phoneNumber"
                  [readonly]="!isEditing"
                  class="w-full px-3 py-2 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  [class]="isEditing ?
                    (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                    (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                  placeholder="Enter phone number"
                >
              </div>

              <!-- Role (Read-only) -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Role</label>
                <input
                  type="text"
                  [(ngModel)]="profileData.role"
                  name="role"
                  readonly
                  class="w-full px-3 py-2 rounded-lg border cursor-not-allowed"
                  [class]="isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-gray-100 border-gray-300 text-gray-600'"
                  placeholder="Role"
                >
                <p class="text-xs mt-1" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'">
                  Role cannot be changed. Contact administrator for role changes.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Security & Preferences Card -->
        <div class="rounded-lg shadow-sm border" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
          <div class="px-6 py-3 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
            <h2 class="text-lg font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Security & Preferences</h2>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <!-- Password Change -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Password</h3>
                  <p class="text-xs" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-500'">Update your password to keep your account secure</p>
                </div>
                <button (click)="openChangePasswordModal()"
                  class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
                  [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
                >
                  Change Password
                </button>
              </div>

              <!-- Account Status -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Account Status</h3>
                  <p class="text-xs" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-500'">Your account is currently active</p>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-sm font-medium text-green-600">Active</span>
                </div>
              </div>

              <!-- Last Login -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Last Login</h3>
                  <p class="text-xs" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-500'">Your most recent login activity</p>
                </div>
                <span class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">
                  Today at 2:30 PM
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Professional Upload Modal -->
<div *ngIf="showUploadModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden modal-animation">

    <!-- Modal Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Upload Profile Photo</h3>
        <button
          (click)="closeUploadModal()"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          [disabled]="isUploading">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Modal Body -->
    <div class="p-6">
      <!-- Preview Image -->
      <div *ngIf="previewUrl && !isUploading && !uploadSuccess && !uploadError" class="text-center mb-6">
        <div class="inline-block relative">
          <img [src]="previewUrl" alt="Preview" class="w-32 h-32 rounded-full object-cover shadow-lg">
        </div>
      </div>

      <!-- Simple confirmation text -->
      <div *ngIf="!isUploading && !uploadSuccess && !uploadError" class="text-center mb-4">
        <p class="text-lg text-gray-800 dark:text-gray-200 mb-3">Do you want to upload this photo?</p>

        <!-- Professional Ethics Statement -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 text-sm">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <div class="text-blue-800 dark:text-blue-200">
              <p class="font-medium mb-1">Professional Photo Guidelines</p>
              <p class="text-xs leading-relaxed">
                Please ensure your photo is appropriate for an academic environment.
                Inappropriate content including but not limited to explicit, offensive, or unprofessional images is strictly prohibited and may result in account suspension.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Upload Progress -->
      <div *ngIf="isUploading" class="text-center">
        <div class="mb-4">
          <div class="w-16 h-16 mx-auto mb-4">
            <svg class="animate-spin w-16 h-16 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <p class="text-gray-600 dark:text-gray-400 mb-2">Uploading your photo...</p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" [style.width.%]="uploadProgress"></div>
          </div>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">{{ uploadProgress.toFixed(0) }}%</p>
        </div>
      </div>

      <!-- Success State -->
      <div *ngIf="uploadSuccess" class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 text-green-500 success-animation">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <p class="text-green-600 dark:text-green-400 font-medium">Photo uploaded successfully!</p>
      </div>

      <!-- Error State -->
      <div *ngIf="uploadError" class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 text-red-500 error-animation">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <p class="text-red-600 dark:text-red-400 font-medium mb-2">Upload Failed</p>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ uploadError }}</p>
      </div>
    </div>

    <!-- Modal Footer -->
    <div *ngIf="!isUploading && !uploadSuccess" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
      <button
        (click)="closeUploadModal()"
        class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
        Cancel
      </button>
      <button
        *ngIf="!uploadError"
        (click)="uploadProfilePhoto()"
        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium">
        Upload Photo
      </button>
      <button
        *ngIf="uploadError"
        (click)="uploadError = null"
        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium">
        Try Again
      </button>
    </div>
  </div>
</div>

<!-- Change Password Modal -->
<div *ngIf="showChangePasswordModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
    <!-- Modal Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Change Password</h3>
        <button
          (click)="closeChangePasswordModal()"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          [disabled]="isChangingPassword">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="p-6">
      <form (ngSubmit)="changePassword()" #passwordForm="ngForm">
        <!-- Current Password -->
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Current Password</label>
          <div class="relative">
            <input
              [type]="showCurrentPassword ? 'text' : 'password'"
              [(ngModel)]="passwordData.currentPassword"
              name="currentPassword"
              required
              class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter current password"
            >
            <button
              type="button"
              (click)="toggleCurrentPasswordVisibility()"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <svg *ngIf="!showCurrentPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
              <svg *ngIf="showCurrentPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </div>

        <!-- New Password -->
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">New Password</label>
          <div class="relative">
            <input
              [type]="showNewPassword ? 'text' : 'password'"
              [(ngModel)]="passwordData.newPassword"
              name="newPassword"
              required
              minlength="8"
              class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter new password"
            >
            <button
              type="button"
              (click)="toggleNewPasswordVisibility()"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <svg *ngIf="!showNewPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
              <svg *ngIf="showNewPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Confirm Password -->
        <div class="mb-6">
          <label class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Confirm New Password</label>
          <div class="relative">
            <input
              [type]="showConfirmPassword ? 'text' : 'password'"
              [(ngModel)]="passwordData.confirmPassword"
              name="confirmPassword"
              required
              class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Confirm new password"
            >
            <button
              type="button"
              (click)="toggleConfirmPasswordVisibility()"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <svg *ngIf="!showConfirmPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
              <svg *ngIf="showConfirmPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Error Message -->
        <div *ngIf="passwordError" class="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p class="text-sm text-red-600 dark:text-red-400">{{ passwordError }}</p>
        </div>

        <!-- Success Message -->
        <div *ngIf="passwordSuccess" class="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p class="text-sm text-green-600 dark:text-green-400">{{ passwordSuccess }}</p>
        </div>
      </form>
    </div>

    <!-- Modal Footer -->
    <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 flex justify-end space-x-3">
      <button
        (click)="closeChangePasswordModal()"
        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors duration-200"
        [disabled]="isChangingPassword"
      >
        Cancel
      </button>
      <button
        (click)="changePassword()"
        [disabled]="isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword"
        class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span *ngIf="!isChangingPassword">Change Password</span>
        <span *ngIf="isChangingPassword" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Changing...
        </span>
      </button>
    </div>
  </div>
</div>

<!-- Toast Notifications -->
<app-toast></app-toast>
