/* Activity Logs Component Styles */
.activity-logs-container {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Console Styles */
.log-console {
  max-height: 600px;
  overflow-y: auto;
  font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
  line-height: 1.4;
}

.log-console::-webkit-scrollbar {
  width: 8px;
}

.log-console::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.log-console::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.log-console::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .log-console::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .log-console::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .log-console::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.log-entry {
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
}

.log-entry:hover {
  border-left-color: #F97316;
}

/* Stats cards animation */
.grid > div {
  animation: slideInUp 0.6s ease forwards;
}

.grid > div:nth-child(1) { animation-delay: 0.1s; }
.grid > div:nth-child(2) { animation-delay: 0.2s; }
.grid > div:nth-child(3) { animation-delay: 0.3s; }
.grid > div:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table hover effects */
tbody tr {
  transition: background-color 0.2s ease;
}

/* Button hover effects */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Log level indicators */
.level-info { border-left: 4px solid #3B82F6; }
.level-warning { border-left: 4px solid #F59E0B; }
.level-error { border-left: 4px solid #EF4444; }
.level-critical { border-left: 4px solid #DC2626; }

/* Action badges */
.action-badge {
  transition: all 0.2s ease;
}

.action-badge:hover {
  transform: scale(1.05);
}

/* Search input focus effects */
input:focus {
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

/* Dark mode specific styles */
[data-theme="dark"] input:focus {
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
}

/* Timestamp styling */
.timestamp-cell {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* IP Address styling */
.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

/* Pagination styles */
.pagination button {
  transition: all 0.2s ease;
}

.pagination button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .flex.space-x-3 {
    flex-direction: column;
    space-x: 0;
  }
  
  .flex.space-x-3 > * {
    margin-bottom: 0.75rem;
  }
  
  .pagination {
    flex-direction: column;
    align-items: center;
    space-y: 1rem;
  }
}

/* Table responsiveness */
@media (max-width: 1024px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  table {
    min-width: 1100px;
  }
}

/* Log entry animations */
.log-entry {
  animation: slideInLeft 0.3s ease forwards;
}

.log-entry:nth-child(1) { animation-delay: 0.05s; }
.log-entry:nth-child(2) { animation-delay: 0.1s; }
.log-entry:nth-child(3) { animation-delay: 0.15s; }
.log-entry:nth-child(4) { animation-delay: 0.2s; }
.log-entry:nth-child(5) { animation-delay: 0.25s; }
.log-entry:nth-child(6) { animation-delay: 0.3s; }
.log-entry:nth-child(7) { animation-delay: 0.35s; }
.log-entry:nth-child(8) { animation-delay: 0.4s; }
.log-entry:nth-child(9) { animation-delay: 0.45s; }
.log-entry:nth-child(10) { animation-delay: 0.5s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Console text selection */
.log-console {
  user-select: text;
}

.log-entry span {
  user-select: text;
}

/* Live indicator animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
